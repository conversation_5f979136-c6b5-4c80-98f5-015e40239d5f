# MirrorGo 简化配置文件

# 域名到端口的映射
domain_ports:
  # GitHub 主域名
  github.com: 9080

  # GitHub 相关子域名
  github.githubassets.com: 9082      # 静态资源 (CSS, JS, 图片)
  api.github.com: 9083               # API 接口
  collector.github.com: 9084         # 数据收集
  avatars.githubusercontent.com: 9085 # 用户头像
  raw.githubusercontent.com: 9086    # 原始文件内容
  camo.githubusercontent.com: 9087   # 图片代理

  # 其他网站
  stackoverflow.com: 9081

# 可选的代理配置（如果需要通过上游代理访问）
proxy:
  enable: true
  url: "http://127.0.0.1:7890"

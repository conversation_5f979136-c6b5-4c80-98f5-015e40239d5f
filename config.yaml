# MirrorGo 配置文件

# 域名映射配置 - 支持灵活的部署场景
domains:
  # GitHub 主域名
  - source: "github.com"           # 源目标域名
    port: 9080                     # 本地监听端口
    target: "localhost:9080"       # 替换后的访问地址 (开发环境)
    # target: "github.example.com" # 生产环境示例

  # GitHub 相关子域名
  - source: "github.githubassets.com"
    port: 9082
    target: "localhost:9082"       # 静态资源 (CSS, JS, 图片)
    # target: "assets.example.com"

  - source: "api.github.com"
    port: 9083
    target: "localhost:9083"       # API 接口
    # target: "api.example.com"

  - source: "collector.github.com"
    port: 9084
    target: "localhost:9084"       # 数据收集
    # target: "collector.example.com"

  - source: "avatars.githubusercontent.com"
    port: 9085
    target: "localhost:9085"       # 用户头像
    # target: "avatars.example.com"

  - source: "raw.githubusercontent.com"
    port: 9086
    target: "localhost:9086"       # 原始文件内容
    # target: "raw.example.com"

  - source: "camo.githubusercontent.com"
    port: 9087
    target: "localhost:9087"       # 图片代理
    # target: "camo.example.com"

  # 其他网站
  - source: "stackoverflow.com"
    port: 9081
    target: "localhost:9081"
    # target: "so.example.com"

# 可选的代理配置（如果需要通过上游代理访问）
proxy:
  enable: true
  url: "http://127.0.0.1:7890"

# MirrorGo 代理服务器配置文件

# 域名到端口的映射
# 每个域名将在指定的本地端口上提供代理服务
domain_ports:
  github.com: 8080
  stackoverflow.com: 8081
  developer.mozilla.org: 8082
  npmjs.com: 8083
  golang.org: 8084

# 可选的 HTTP 代理配置
# 如果你需要通过上游代理访问目标网站，请启用此配置
proxy:
  enable: false  # 设置为 true 启用代理
  url: "http://proxy.example.com:8080"  # 代理服务器地址
  username: ""  # 代理用户名（可选）
  password: ""  # 代理密码（可选）

# 内容改写配置
rewrite:
  enable_html: true   # 启用 HTML 内容改写
  enable_js: true     # 启用 JavaScript 内容改写
  enable_css: true    # 启用 CSS 内容改写
  
  # 需要改写的 HTML 属性列表
  html_attributes:
    - "src"
    - "href"
    - "action"
    - "data-src"
    - "data-href"
    - "data-url"
    - "poster"
    - "background"

# 服务器配置
server:
  read_timeout: 30    # 读取超时时间（秒）
  write_timeout: 30   # 写入超时时间（秒）
  idle_timeout: 60    # 空闲超时时间（秒）

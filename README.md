# MirrorGo

本项目实现了一个 **基于多端口的反向代理系统**，用于绕过防火墙访问被屏蔽的网站。每个目标域名映射到本地一个独立端口，并在返回 HTML 页面时自动将页面中的资源链接改写为本地端口形式，从而使浏览器无需设置代理即可完整加载页面内容。

---

## 📌 项目特性

- ✅ **多端口监听** - 每个端口反代一个独立域名
- ✅ **智能链接改写** - 自动修改 HTML 中的 `src` / `href` / `action` 等链接
- ✅ **全协议支持** - 支持 HTTP 和 HTTPS 目标网站
- ✅ **零配置浏览器** - 浏览器无需设置代理，直接访问本地端口即可
- ✅ **灵活配置** - 可配置扩展多个域名及对应端口
- ✅ **高级改写** - 支持 JS 和 CSS 的深度链接改写
- ✅ **上游代理** - 支持可选的 HTTP 代理配置
- ✅ **高性能** - 基于 Go 语言的高性能代理服务器

---

## 🚀 快速开始

### 1. 编译项目

```bash
git clone <repository-url>
cd mirrorgo
go build -o mirrorgo cmd/mirrorgo/main.go
```

### 2. 配置文件

编辑 `config.yaml` 文件：

```yaml
# 域名到端口的映射
domain_ports:
  github.com: 8080
  stackoverflow.com: 8081

# 可选的代理配置（如果需要通过上游代理访问）
# proxy:
#   enable: true
#   url: "http://proxy.example.com:8080"
```

### 3. 启动服务

```bash
./mirrorgo -config config.yaml
```

### 4. 访问网站

- GitHub: http://localhost:8080
- Stack Overflow: http://localhost:8081

---

## ⚙️ 配置说明

### 基本配置

```yaml
# 域名端口映射
domain_ports:
  example.com: 8080
  another.com: 8081
```

### 高级配置

```yaml
# 内容改写配置
rewrite:
  enable_html: true   # 启用 HTML 改写
  enable_js: true     # 启用 JavaScript 改写
  enable_css: true    # 启用 CSS 改写
  html_attributes:    # 自定义改写的 HTML 属性
    - "src"
    - "href"
    - "action"

# 服务器配置
server:
  read_timeout: 30    # 读取超时（秒）
  write_timeout: 30   # 写入超时（秒）
  idle_timeout: 60    # 空闲超时（秒）

# 上游代理配置
proxy:
  enable: true
  url: "http://proxy.example.com:8080"
  username: "user"    # 可选
  password: "pass"    # 可选
```

---

## 🔧 工作原理

1. **多端口监听**: 为每个配置的域名启动独立的 HTTP 服务器
2. **请求转发**: 将本地请求转发到目标域名
3. **内容改写**: 分析响应内容，将其中的链接改写为本地端口形式
4. **透明代理**: 浏览器感知不到代理的存在，正常浏览网页

### 链接改写示例

原始 HTML:
```html
<a href="https://github.com/user/repo">Repository</a>
<img src="https://avatars.githubusercontent.com/u/123">
```

改写后:
```html
<a href="http://localhost:8080/user/repo">Repository</a>
<img src="/proxy/https/avatars.githubusercontent.com/u/123">
```

---

## 🧪 测试

运行测试：

```bash
# 使用 Makefile（推荐）
make test                 # 运行所有测试
make test-verbose         # 详细测试输出
make test-coverage        # 生成覆盖率报告

# 直接使用 go 命令
go test ./...             # 运行所有测试
go test ./pkg/config -v   # 运行特定模块测试
```

当前测试覆盖率：
- 配置模块：88.4%
- 改写器模块：59.1%

---

## �️ 开发工具

项目提供了完整的 Makefile 支持：

```bash
make help                 # 查看所有可用命令
make build                # 构建应用程序
make build-all            # 构建多平台版本
make test-coverage        # 生成测试覆盖率报告
make run                  # 构建并运行
make dev                  # 开发模式运行
make check                # 代码质量检查
```

---

## �📁 项目结构

```
mirrorgo/
├── cmd/mirrorgo/          # 主程序入口
├── pkg/
│   ├── config/           # 配置管理
│   ├── proxy/            # 代理服务器
│   ├── rewriter/         # 内容改写器
│   └── utils/            # 工具函数
├── examples/             # 示例配置
├── tests/               # 测试文件
├── build/               # 构建输出
├── Makefile             # 构建工具
├── USAGE.md             # 详细使用指南
└── README.md            # 项目说明
```

---

## 📚 文档

- [README.md](README.md) - 项目概述和快速开始
- [USAGE.md](USAGE.md) - 详细使用指南
- [examples/config.yaml](examples/config.yaml) - 完整配置示例

---

## 🤝 贡献

欢迎提交 Issue 和 Pull Request！

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

---

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。
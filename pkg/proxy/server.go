package proxy

import (
	"compress/gzip"
	"context"
	"crypto/tls"
	"fmt"
	"io"
	"net"
	"net/http"
	"strconv"
	"strings"
	"sync"
	"time"

	"mirrorgo/pkg/config"
	"mirrorgo/pkg/rewriter"
)

// Server 代理服务器
type Server struct {
	config   *config.Config
	servers  []*http.Server
	rewriter *rewriter.ContentRewriter
	client   *http.Client
	mu       sync.RWMutex
}

// NewServer 创建新的代理服务器
func NewServer(cfg *config.Config) (*Server, error) {
	// 创建 HTTP 客户端
	client, err := createHTTPClient(cfg)
	if err != nil {
		return nil, fmt.Errorf("创建 HTTP 客户端失败: %w", err)
	}

	// 创建内容改写器
	contentRewriter := rewriter.NewContentRewriter(cfg)

	return &Server{
		config:   cfg,
		servers:  make([]*http.Server, 0),
		rewriter: contentRewriter,
		client:   client,
	}, nil
}

// Start 启动所有代理服务器
func (s *Server) Start() error {
	s.mu.Lock()
	defer s.mu.Unlock()

	for domain, port := range s.config.DomainPorts {
		server := &http.Server{
			Addr:         fmt.Sprintf(":%d", port),
			Handler:      s.createHandler(domain),
			ReadTimeout:  time.Duration(s.config.ServerConfig.ReadTimeout) * time.Second,
			WriteTimeout: time.Duration(s.config.ServerConfig.WriteTimeout) * time.Second,
			IdleTimeout:  time.Duration(s.config.ServerConfig.IdleTimeout) * time.Second,
		}

		s.servers = append(s.servers, server)

		// 启动服务器
		go func(srv *http.Server, targetDomain string) {
			if err := srv.ListenAndServe(); err != nil && err != http.ErrServerClosed {
				fmt.Printf("端口 %s 服务器错误: %v\n", srv.Addr, err)
			}
		}(server, domain)
	}

	return nil
}

// Stop 停止所有代理服务器
func (s *Server) Stop() {
	s.mu.Lock()
	defer s.mu.Unlock()

	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	for _, server := range s.servers {
		if err := server.Shutdown(ctx); err != nil {
			fmt.Printf("关闭服务器失败: %v\n", err)
		}
	}

	s.servers = s.servers[:0]
}

// createHandler 为指定域名创建处理器
func (s *Server) createHandler(targetDomain string) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		// 构建目标 URL
		targetURL := s.buildTargetURL(targetDomain, r)

		// 创建代理请求
		proxyReq, err := http.NewRequest(r.Method, targetURL, r.Body)
		if err != nil {
			http.Error(w, fmt.Sprintf("创建代理请求失败: %v", err), http.StatusInternalServerError)
			return
		}

		// 复制请求头
		s.copyHeaders(r.Header, proxyReq.Header, targetDomain)

		// 发送请求
		resp, err := s.client.Do(proxyReq)
		if err != nil {
			http.Error(w, fmt.Sprintf("代理请求失败: %v", err), http.StatusBadGateway)
			return
		}
		defer resp.Body.Close()

		// 复制响应头
		s.copyResponseHeaders(resp.Header, w.Header())

		// 设置状态码
		w.WriteHeader(resp.StatusCode)

		// 处理响应内容
		if err := s.handleResponse(w, resp, targetDomain); err != nil {
			fmt.Printf("处理响应失败: %v\n", err)
		}
	}
}

// buildTargetURL 构建目标 URL
func (s *Server) buildTargetURL(domain string, r *http.Request) string {
	scheme := "https"
	if strings.Contains(r.Header.Get("X-Forwarded-Proto"), "http") {
		scheme = "http"
	}

	// 多端口架构：每个端口对应一个固定域名，不需要路径解析

	targetURL := fmt.Sprintf("%s://%s%s", scheme, domain, r.URL.Path)
	if r.URL.RawQuery != "" {
		targetURL += "?" + r.URL.RawQuery
	}

	return targetURL
}

// copyHeaders 复制请求头
func (s *Server) copyHeaders(src, dst http.Header, targetDomain string) {
	for key, values := range src {
		// 跳过一些不应该转发的头
		if s.shouldSkipHeader(key) {
			continue
		}

		// 修改 Host 头
		if strings.ToLower(key) == "host" {
			dst.Set(key, targetDomain)
			continue
		}

		// 修改 Referer 头
		if strings.ToLower(key) == "referer" {
			for _, value := range values {
				if newReferer := s.rewriteReferer(value); newReferer != "" {
					dst.Add(key, newReferer)
				}
			}
			continue
		}

		// 复制其他头
		for _, value := range values {
			dst.Add(key, value)
		}
	}

	// 设置必要的头
	dst.Set("User-Agent", "MirrorGo/1.0")
}

// copyResponseHeaders 复制响应头
func (s *Server) copyResponseHeaders(src, dst http.Header) {
	for key, values := range src {
		// 跳过一些不应该转发的头
		if s.shouldSkipResponseHeader(key) {
			continue
		}

		// 特殊处理 CSP 头
		if strings.ToLower(key) == "content-security-policy" {
			for _, value := range values {
				rewrittenCSP := s.rewriteCSP(value)
				dst.Add(key, rewrittenCSP)
			}
			continue
		}

		for _, value := range values {
			dst.Add(key, value)
		}
	}
}

// shouldSkipHeader 判断是否应该跳过请求头
func (s *Server) shouldSkipHeader(key string) bool {
	skipHeaders := []string{
		"connection", "proxy-connection", "upgrade", "te", "trailer",
		"proxy-authorization", "proxy-authenticate",
		"accept-encoding", // 跳过压缩请求头，避免内容压缩问题
	}

	lowerKey := strings.ToLower(key)
	for _, skip := range skipHeaders {
		if lowerKey == skip {
			return true
		}
	}

	return false
}

// shouldSkipResponseHeader 判断是否应该跳过响应头
func (s *Server) shouldSkipResponseHeader(key string) bool {
	skipHeaders := []string{
		"connection", "proxy-connection", "upgrade", "te", "trailer",
		"content-encoding", "content-length", // 这些会在内容改写后重新设置
	}

	lowerKey := strings.ToLower(key)
	for _, skip := range skipHeaders {
		if lowerKey == skip {
			return true
		}
	}

	return false
}

// rewriteReferer 改写 Referer 头
func (s *Server) rewriteReferer(referer string) string {
	// 这里可以实现 Referer 的改写逻辑
	// 暂时直接返回原值
	return referer
}

// rewriteCSP 改写 Content-Security-Policy 头
func (s *Server) rewriteCSP(csp string) string {
	// 将 CSP 中的域名替换为对应的目标地址
	rewrittenCSP := csp

	for domain, target := range s.config.DomainTargets {
		// 替换各种 CSP 指令中的域名
		rewrittenCSP = strings.ReplaceAll(rewrittenCSP, domain, target)

		// 也替换带协议的完整 URL
		// 根据目标地址是否包含 localhost 来决定使用 http 还是 https
		if strings.Contains(target, "localhost:") {
			rewrittenCSP = strings.ReplaceAll(rewrittenCSP, "https://"+domain, "http://"+target)
			rewrittenCSP = strings.ReplaceAll(rewrittenCSP, "http://"+domain, "http://"+target)
		} else {
			rewrittenCSP = strings.ReplaceAll(rewrittenCSP, "https://"+domain, "https://"+target)
			rewrittenCSP = strings.ReplaceAll(rewrittenCSP, "http://"+domain, "https://"+target)
		}
	}

	// 处理 'none' 关键字的问题
	// 如果指令包含 'none'，我们需要将其替换为允许的源
	directives := []string{
		"default-src", "script-src", "style-src", "img-src",
		"connect-src", "font-src", "media-src", "frame-src",
	}

	for _, directive := range directives {
		directivePattern := directive + " "
		if strings.Contains(rewrittenCSP, directivePattern) {
			// 查找指令的完整值
			start := strings.Index(rewrittenCSP, directivePattern)
			if start != -1 {
				start += len(directivePattern)
				end := strings.Index(rewrittenCSP[start:], ";")
				if end == -1 {
					end = len(rewrittenCSP) - start
				}

				directiveValue := rewrittenCSP[start : start+end]

				// 如果包含 'none' 和其他源，移除 'none'
				if strings.Contains(directiveValue, "'none'") && strings.Contains(directiveValue, "localhost:") {
					directiveValue = strings.ReplaceAll(directiveValue, "'none'", "")
					directiveValue = strings.ReplaceAll(directiveValue, "  ", " ")
					directiveValue = strings.TrimSpace(directiveValue)

					// 重新构建 CSP
					newDirective := directive + " " + directiveValue
					oldDirective := directive + " " + rewrittenCSP[start:start+end]
					rewrittenCSP = strings.Replace(rewrittenCSP, oldDirective, newDirective, 1)
				}
			}
		}
	}

	return rewrittenCSP
}

// handleResponse 处理响应内容
func (s *Server) handleResponse(w http.ResponseWriter, resp *http.Response, targetDomain string) error {
	contentType := resp.Header.Get("Content-Type")
	contentEncoding := resp.Header.Get("Content-Encoding")

	// 判断是否需要改写内容
	if s.shouldRewriteContent(contentType) {
		// 获取响应体读取器
		var bodyReader io.Reader = resp.Body

		// 如果内容被压缩，需要解压缩
		if contentEncoding == "gzip" {
			gzipReader, err := gzip.NewReader(resp.Body)
			if err != nil {
				return fmt.Errorf("创建 gzip 读取器失败: %w", err)
			}
			defer gzipReader.Close()
			bodyReader = gzipReader
		}

		// 读取响应内容
		body, err := io.ReadAll(bodyReader)
		if err != nil {
			return fmt.Errorf("读取响应内容失败: %w", err)
		}

		// 改写内容
		rewrittenBody, err := s.rewriter.RewriteContent(body, contentType, targetDomain, s.config.DomainTargets)
		if err != nil {
			return fmt.Errorf("改写内容失败: %w", err)
		}

		// 移除压缩相关的头，因为我们返回的是未压缩的内容
		w.Header().Del("Content-Encoding")
		w.Header().Del("Content-Length")

		// 设置新的 Content-Length
		w.Header().Set("Content-Length", strconv.Itoa(len(rewrittenBody)))

		// 写入改写后的内容
		_, err = w.Write(rewrittenBody)
		return err
	}

	// 对于不需要改写的内容，直接复制
	// 但仍需要处理压缩
	if contentEncoding == "gzip" {
		// 解压缩后直接传输
		gzipReader, err := gzip.NewReader(resp.Body)
		if err != nil {
			return fmt.Errorf("创建 gzip 读取器失败: %w", err)
		}
		defer gzipReader.Close()

		// 移除压缩头
		w.Header().Del("Content-Encoding")
		w.Header().Del("Content-Length")

		_, err = io.Copy(w, gzipReader)
		return err
	}

	// 直接复制内容
	_, err := io.Copy(w, resp.Body)
	return err
}

// shouldRewriteContent 判断是否需要改写内容
func (s *Server) shouldRewriteContent(contentType string) bool {
	contentType = strings.ToLower(contentType)

	if s.config.RewriteConfig.EnableHTML && strings.Contains(contentType, "text/html") {
		return true
	}

	if s.config.RewriteConfig.EnableJS && strings.Contains(contentType, "javascript") {
		return true
	}

	if s.config.RewriteConfig.EnableCSS && strings.Contains(contentType, "text/css") {
		return true
	}

	// 检查 JSON 内容（包括 manifest.json），但排除 JavaScript
	if strings.Contains(contentType, "json") && !strings.Contains(contentType, "javascript") {
		return true
	}

	return false
}

// createHTTPClient 创建 HTTP 客户端
func createHTTPClient(cfg *config.Config) (*http.Client, error) {
	transport := &http.Transport{
		DialContext: (&net.Dialer{
			Timeout:   30 * time.Second,
			KeepAlive: 30 * time.Second,
		}).DialContext,
		MaxIdleConns:          100,
		IdleConnTimeout:       90 * time.Second,
		TLSHandshakeTimeout:   10 * time.Second,
		ExpectContinueTimeout: 1 * time.Second,
		TLSClientConfig: &tls.Config{
			InsecureSkipVerify: true, // 跳过 TLS 验证以支持自签名证书
		},
	}

	// 设置代理
	if cfg.ProxyConfig != nil && cfg.ProxyConfig.Enable {
		proxyURL, err := cfg.GetProxyURL()
		if err != nil {
			return nil, fmt.Errorf("获取代理 URL 失败: %w", err)
		}
		if proxyURL != nil {
			transport.Proxy = http.ProxyURL(proxyURL)
		}
	}

	return &http.Client{
		Transport: transport,
		Timeout:   60 * time.Second,
	}, nil
}

package config

import (
	"fmt"
	"io/ioutil"
	"net/url"

	"gopkg.in/yaml.v3"
)

// DomainMapping 域名映射配置
type DomainMapping struct {
	// Source 源目标域名
	Source string `yaml:"source"`

	// Port 本地监听端口
	Port int `yaml:"port"`

	// Target 替换后的访问地址
	Target string `yaml:"target"`
}

// Config 代理服务器配置
type Config struct {
	// Domains 域名映射配置列表
	Domains []DomainMapping `yaml:"domains"`

	// DomainPorts 域名到端口的映射 (从 Domains 生成，向后兼容)
	DomainPorts map[string]int `yaml:"domain_ports,omitempty"`

	// DomainTargets 域名到目标地址的映射 (从 Domains 生成)
	DomainTargets map[string]string `yaml:"-"`

	// ProxyConfig 可选的 HTTP 代理配置
	ProxyConfig *ProxyConfig `yaml:"proxy,omitempty"`

	// RewriteConfig 内容改写配置
	RewriteConfig *RewriteConfig `yaml:"rewrite,omitempty"`

	// ServerConfig 服务器配置
	ServerConfig *ServerConfig `yaml:"server,omitempty"`
}

// ProxyConfig HTTP 代理配置
type ProxyConfig struct {
	// Enable 是否启用代理
	Enable bool `yaml:"enable"`

	// URL 代理服务器地址 (例如: http://proxy.example.com:8080)
	URL string `yaml:"url"`

	// Username 代理用户名 (可选)
	Username string `yaml:"username,omitempty"`

	// Password 代理密码 (可选)
	Password string `yaml:"password,omitempty"`
}

// RewriteConfig 内容改写配置
type RewriteConfig struct {
	// EnableHTML 是否启用 HTML 改写
	EnableHTML bool `yaml:"enable_html"`

	// EnableJS 是否启用 JavaScript 改写
	EnableJS bool `yaml:"enable_js"`

	// EnableCSS 是否启用 CSS 改写
	EnableCSS bool `yaml:"enable_css"`

	// HTMLAttributes 需要改写的 HTML 属性列表
	HTMLAttributes []string `yaml:"html_attributes,omitempty"`
}

// ServerConfig 服务器配置
type ServerConfig struct {
	// ReadTimeout 读取超时时间 (秒)
	ReadTimeout int `yaml:"read_timeout"`

	// WriteTimeout 写入超时时间 (秒)
	WriteTimeout int `yaml:"write_timeout"`

	// IdleTimeout 空闲超时时间 (秒)
	IdleTimeout int `yaml:"idle_timeout"`
}

// LoadConfig 从文件加载配置
func LoadConfig(path string) (*Config, error) {
	data, err := ioutil.ReadFile(path)
	if err != nil {
		return nil, fmt.Errorf("读取配置文件失败: %w", err)
	}

	var config Config
	if err := yaml.Unmarshal(data, &config); err != nil {
		return nil, fmt.Errorf("解析配置文件失败: %w", err)
	}

	// 设置默认值
	if err := config.setDefaults(); err != nil {
		return nil, fmt.Errorf("设置默认配置失败: %w", err)
	}

	// 验证配置
	if err := config.validate(); err != nil {
		return nil, fmt.Errorf("配置验证失败: %w", err)
	}

	return &config, nil
}

// setDefaults 设置默认配置值
func (c *Config) setDefaults() error {
	// 处理新旧配置格式的兼容性
	if len(c.Domains) == 0 && len(c.DomainPorts) > 0 {
		// 从旧格式转换到新格式
		for domain, port := range c.DomainPorts {
			c.Domains = append(c.Domains, DomainMapping{
				Source: domain,
				Port:   port,
				Target: fmt.Sprintf("localhost:%d", port),
			})
		}
	}

	// 生成映射表
	if c.DomainPorts == nil {
		c.DomainPorts = make(map[string]int)
	}
	if c.DomainTargets == nil {
		c.DomainTargets = make(map[string]string)
	}

	for _, mapping := range c.Domains {
		c.DomainPorts[mapping.Source] = mapping.Port
		c.DomainTargets[mapping.Source] = mapping.Target
	}

	// 设置默认的改写配置
	if c.RewriteConfig == nil {
		c.RewriteConfig = &RewriteConfig{}
	}

	if c.RewriteConfig.HTMLAttributes == nil {
		c.RewriteConfig.HTMLAttributes = []string{
			"src", "href", "action", // 基本属性
			"data-src", "data-href", "data-base-href", // 懒加载和数据属性
			"content",                     // meta 标签内容
			"poster",                      // 视频封面
			"background",                  // 背景图片
			"data-url", "data-background", // 其他数据属性
		}
	}

	// 默认启用 HTML 改写
	if !c.RewriteConfig.EnableHTML && !c.RewriteConfig.EnableJS && !c.RewriteConfig.EnableCSS {
		c.RewriteConfig.EnableHTML = true
		c.RewriteConfig.EnableJS = true
		c.RewriteConfig.EnableCSS = true
	}

	// 设置默认的服务器配置
	if c.ServerConfig == nil {
		c.ServerConfig = &ServerConfig{
			ReadTimeout:  30,
			WriteTimeout: 30,
			IdleTimeout:  60,
		}
	}

	return nil
}

// validate 验证配置
func (c *Config) validate() error {
	if len(c.Domains) == 0 && len(c.DomainPorts) == 0 {
		return fmt.Errorf("至少需要配置一个域名映射")
	}

	// 验证域名映射配置
	for i, mapping := range c.Domains {
		if mapping.Source == "" {
			return fmt.Errorf("第 %d 个域名映射的源域名不能为空", i+1)
		}

		if mapping.Port < 1 || mapping.Port > 65535 {
			return fmt.Errorf("域名 %s 的端口 %d 超出有效范围 (1-65535)", mapping.Source, mapping.Port)
		}

		if mapping.Target == "" {
			return fmt.Errorf("域名 %s 的目标地址不能为空", mapping.Source)
		}
	}

	// 验证端口范围 (向后兼容)
	for domain, port := range c.DomainPorts {
		if port < 1 || port > 65535 {
			return fmt.Errorf("域名 %s 的端口 %d 超出有效范围 (1-65535)", domain, port)
		}
	}

	// 验证代理配置
	if c.ProxyConfig != nil && c.ProxyConfig.Enable {
		if c.ProxyConfig.URL == "" {
			return fmt.Errorf("启用代理时必须提供代理 URL")
		}

		if _, err := url.Parse(c.ProxyConfig.URL); err != nil {
			return fmt.Errorf("代理 URL 格式无效: %w", err)
		}
	}

	return nil
}

// GetProxyURL 获取代理 URL
func (c *Config) GetProxyURL() (*url.URL, error) {
	if c.ProxyConfig == nil || !c.ProxyConfig.Enable {
		return nil, nil
	}

	proxyURL, err := url.Parse(c.ProxyConfig.URL)
	if err != nil {
		return nil, err
	}

	// 设置认证信息
	if c.ProxyConfig.Username != "" {
		if c.ProxyConfig.Password != "" {
			proxyURL.User = url.UserPassword(c.ProxyConfig.Username, c.ProxyConfig.Password)
		} else {
			proxyURL.User = url.User(c.ProxyConfig.Username)
		}
	}

	return proxyURL, nil
}

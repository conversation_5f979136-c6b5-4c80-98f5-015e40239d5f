package config

import (
	"io/ioutil"
	"os"
	"testing"
)

func TestLoadConfig(t *testing.T) {
	// 创建临时配置文件
	configContent := `
domain_ports:
  github.com: 8080
  stackoverflow.com: 8081

proxy:
  enable: true
  url: "http://proxy.example.com:8080"
  username: "testuser"
  password: "testpass"

rewrite:
  enable_html: true
  enable_js: true
  enable_css: true
  html_attributes:
    - "src"
    - "href"

server:
  read_timeout: 30
  write_timeout: 30
  idle_timeout: 60
`

	tmpFile, err := ioutil.TempFile("", "config_test_*.yaml")
	if err != nil {
		t.Fatalf("创建临时文件失败: %v", err)
	}
	defer os.Remove(tmpFile.Name())

	if _, err := tmpFile.WriteString(configContent); err != nil {
		t.Fatalf("写入临时文件失败: %v", err)
	}
	tmpFile.Close()

	// 测试加载配置
	config, err := LoadConfig(tmpFile.Name())
	if err != nil {
		t.Fatalf("加载配置失败: %v", err)
	}

	// 验证域名端口映射
	if len(config.DomainPorts) != 2 {
		t.Errorf("期望 2 个域名端口映射，实际得到 %d", len(config.DomainPorts))
	}

	if config.DomainPorts["github.com"] != 8080 {
		t.Errorf("期望 github.com 端口为 8080，实际为 %d", config.DomainPorts["github.com"])
	}

	// 验证代理配置
	if !config.ProxyConfig.Enable {
		t.Error("期望代理配置启用")
	}

	if config.ProxyConfig.URL != "http://proxy.example.com:8080" {
		t.Errorf("期望代理 URL 为 'http://proxy.example.com:8080'，实际为 '%s'", config.ProxyConfig.URL)
	}

	// 验证改写配置
	if !config.RewriteConfig.EnableHTML {
		t.Error("期望 HTML 改写启用")
	}

	if len(config.RewriteConfig.HTMLAttributes) != 2 {
		t.Errorf("期望 2 个 HTML 属性，实际得到 %d", len(config.RewriteConfig.HTMLAttributes))
	}

	// 验证服务器配置
	if config.ServerConfig.ReadTimeout != 30 {
		t.Errorf("期望读取超时为 30，实际为 %d", config.ServerConfig.ReadTimeout)
	}
}

func TestLoadConfigWithDefaults(t *testing.T) {
	// 创建最小配置文件
	configContent := `
domain_ports:
  example.com: 8080
`

	tmpFile, err := ioutil.TempFile("", "config_minimal_*.yaml")
	if err != nil {
		t.Fatalf("创建临时文件失败: %v", err)
	}
	defer os.Remove(tmpFile.Name())

	if _, err := tmpFile.WriteString(configContent); err != nil {
		t.Fatalf("写入临时文件失败: %v", err)
	}
	tmpFile.Close()

	// 测试加载配置
	config, err := LoadConfig(tmpFile.Name())
	if err != nil {
		t.Fatalf("加载配置失败: %v", err)
	}

	// 验证默认值
	if config.RewriteConfig == nil {
		t.Error("期望改写配置不为空")
	}

	if !config.RewriteConfig.EnableHTML {
		t.Error("期望默认启用 HTML 改写")
	}

	if config.ServerConfig == nil {
		t.Error("期望服务器配置不为空")
	}

	if config.ServerConfig.ReadTimeout != 30 {
		t.Errorf("期望默认读取超时为 30，实际为 %d", config.ServerConfig.ReadTimeout)
	}
}

func TestConfigValidation(t *testing.T) {
	tests := []struct {
		name        string
		config      string
		expectError bool
	}{
		{
			name: "有效配置",
			config: `
domain_ports:
  example.com: 8080
`,
			expectError: false,
		},
		{
			name: "空域名端口映射",
			config: `
domain_ports: {}
`,
			expectError: true,
		},
		{
			name: "无效端口范围",
			config: `
domain_ports:
  example.com: 70000
`,
			expectError: true,
		},
		{
			name: "启用代理但无 URL",
			config: `
domain_ports:
  example.com: 8080
proxy:
  enable: true
`,
			expectError: true,
		},
		{
			name: "无效代理 URL",
			config: `
domain_ports:
  example.com: 8080
proxy:
  enable: true
  url: "://invalid-url"
`,
			expectError: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tmpFile, err := ioutil.TempFile("", "config_validation_*.yaml")
			if err != nil {
				t.Fatalf("创建临时文件失败: %v", err)
			}
			defer os.Remove(tmpFile.Name())

			if _, err := tmpFile.WriteString(tt.config); err != nil {
				t.Fatalf("写入临时文件失败: %v", err)
			}
			tmpFile.Close()

			_, err = LoadConfig(tmpFile.Name())
			if tt.expectError && err == nil {
				t.Error("期望出现错误，但没有错误")
			}
			if !tt.expectError && err != nil {
				t.Errorf("不期望出现错误，但得到错误: %v", err)
			}
		})
	}
}

func TestGetProxyURL(t *testing.T) {
	config := &Config{
		ProxyConfig: &ProxyConfig{
			Enable:   true,
			URL:      "http://proxy.example.com:8080",
			Username: "testuser",
			Password: "testpass",
		},
	}

	proxyURL, err := config.GetProxyURL()
	if err != nil {
		t.Fatalf("获取代理 URL 失败: %v", err)
	}

	if proxyURL.Host != "proxy.example.com:8080" {
		t.Errorf("期望主机为 'proxy.example.com:8080'，实际为 '%s'", proxyURL.Host)
	}

	if proxyURL.User.Username() != "testuser" {
		t.Errorf("期望用户名为 'testuser'，实际为 '%s'", proxyURL.User.Username())
	}

	password, _ := proxyURL.User.Password()
	if password != "testpass" {
		t.Errorf("期望密码为 'testpass'，实际为 '%s'", password)
	}
}

func TestGetProxyURLDisabled(t *testing.T) {
	config := &Config{
		ProxyConfig: &ProxyConfig{
			Enable: false,
		},
	}

	proxyURL, err := config.GetProxyURL()
	if err != nil {
		t.Fatalf("获取代理 URL 失败: %v", err)
	}

	if proxyURL != nil {
		t.Error("期望代理 URL 为 nil，因为代理未启用")
	}
}

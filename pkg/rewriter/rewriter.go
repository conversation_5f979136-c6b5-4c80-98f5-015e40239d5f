package rewriter

import (
	"fmt"
	"net/url"
	"regexp"
	"strings"

	"mirrorgo/pkg/config"
)

// ContentRewriter 内容改写器 - 支持新的域名映射格式
type ContentRewriter struct {
	config          *config.Config
	htmlAttrRegexes map[string]*regexp.Regexp
	cssURLRegex     *regexp.Regexp
	jsURLRegexes    []*regexp.Regexp
}

// NewContentRewriter 创建新的内容改写器
func NewContentRewriter(cfg *config.Config) *ContentRewriter {
	rewriter := &ContentRewriter{
		config:          cfg,
		htmlAttrRegexes: make(map[string]*regexp.Regexp),
		jsURLRegexes:    make([]*regexp.Regexp, 0),
	}

	// 初始化 HTML 属性正则表达式
	for _, attr := range cfg.RewriteConfig.HTMLAttributes {
		pattern := fmt.Sprintf(`(?i)(\s%s\s*=\s*["'])([^"']+)(["'])`, regexp.QuoteMeta(attr))
		rewriter.htmlAttrRegexes[attr] = regexp.MustCompile(pattern)
	}

	// 初始化 CSS URL 正则表达式
	rewriter.cssURLRegex = regexp.MustCompile(`url\(([^)]+)\)`)

	// 初始化 JavaScript URL 正则表达式
	rewriter.jsURLRegexes = append(rewriter.jsURLRegexes,
		regexp.MustCompile(`["']https?://[^"']+["']`),
		regexp.MustCompile(`["']/[^"']*["']`),
		regexp.MustCompile(`["']\.\.?/[^"']*["']`),
	)

	return rewriter
}

// RewriteContent 改写内容
func (r *ContentRewriter) RewriteContent(content []byte, contentType, targetDomain string, domainTargets map[string]string) ([]byte, error) {
	contentType = strings.ToLower(contentType)

	if r.config.RewriteConfig.EnableHTML && strings.Contains(contentType, "text/html") {
		return r.rewriteHTML(content, targetDomain, domainTargets)
	}

	if r.config.RewriteConfig.EnableCSS && strings.Contains(contentType, "text/css") {
		return r.rewriteCSS(content, targetDomain, domainTargets)
	}

	if r.config.RewriteConfig.EnableJS && strings.Contains(contentType, "javascript") {
		return r.rewriteJS(content, targetDomain, domainTargets)
	}

	// 添加 JSON 改写支持（特别是 manifest.json）
	if strings.Contains(contentType, "json") {
		return r.rewriteJSON(content, targetDomain, domainTargets)
	}

	return content, nil
}

// rewriteHTML 改写 HTML 内容
func (r *ContentRewriter) rewriteHTML(content []byte, targetDomain string, domainTargets map[string]string) ([]byte, error) {
	result := content

	// 改写各种 HTML 属性
	for attr, regex := range r.htmlAttrRegexes {
		result = regex.ReplaceAllFunc(result, func(match []byte) []byte {
			return r.rewriteHTMLAttribute(match, attr, targetDomain, domainTargets)
		})
	}

	// 改写内联样式中的 URL
	if r.config.RewriteConfig.EnableCSS {
		styleRegex := regexp.MustCompile(`(?i)(\sstyle\s*=\s*["'])([^"']+)(["'])`)
		result = styleRegex.ReplaceAllFunc(result, func(match []byte) []byte {
			return r.rewriteInlineStyle(match, targetDomain, domainTargets)
		})
	}

	// 改写内联脚本中的 URL
	if r.config.RewriteConfig.EnableJS {
		scriptRegex := regexp.MustCompile(`(?i)(<script[^>]*>)(.*?)(</script>)`)
		result = scriptRegex.ReplaceAllFunc(result, func(match []byte) []byte {
			return r.rewriteInlineScript(match, targetDomain, domainTargets)
		})
	}

	return result, nil
}

// rewriteHTMLAttribute 改写 HTML 属性
func (r *ContentRewriter) rewriteHTMLAttribute(match []byte, attr, targetDomain string, domainTargets map[string]string) []byte {
	parts := r.htmlAttrRegexes[attr].FindSubmatch(match)
	if len(parts) != 4 {
		return match
	}

	prefix := parts[1]         // 属性名和等号
	urlStr := string(parts[2]) // URL 值
	suffix := parts[3]         // 引号

	// 改写 URL
	newURL := r.rewriteURL(urlStr, targetDomain, domainTargets)

	return append(append(prefix, []byte(newURL)...), suffix...)
}

// rewriteInlineStyle 改写内联样式
func (r *ContentRewriter) rewriteInlineStyle(match []byte, targetDomain string, domainTargets map[string]string) []byte {
	styleRegex := regexp.MustCompile(`(?i)(\sstyle\s*=\s*["'])([^"']+)(["'])`)
	parts := styleRegex.FindSubmatch(match)
	if len(parts) != 4 {
		return match
	}

	prefix := parts[1]
	styleContent := parts[2]
	suffix := parts[3]

	// 改写样式中的 URL
	newStyleContent := r.cssURLRegex.ReplaceAllFunc(styleContent, func(urlMatch []byte) []byte {
		return r.rewriteCSSURL(urlMatch, targetDomain, domainTargets)
	})

	return append(append(prefix, newStyleContent...), suffix...)
}

// rewriteInlineScript 改写内联脚本
func (r *ContentRewriter) rewriteInlineScript(match []byte, targetDomain string, domainTargets map[string]string) []byte {
	scriptRegex := regexp.MustCompile(`(?i)(<script[^>]*>)(.*?)(</script>)`)
	parts := scriptRegex.FindSubmatch(match)
	if len(parts) != 4 {
		return match
	}

	prefix := parts[1]
	scriptContent := parts[2]
	suffix := parts[3]

	// 改写脚本中的 URL
	newScriptContent := scriptContent
	for _, regex := range r.jsURLRegexes {
		newScriptContent = regex.ReplaceAllFunc(newScriptContent, func(urlMatch []byte) []byte {
			return r.rewriteJSURL(urlMatch, targetDomain, domainTargets)
		})
	}

	return append(append(prefix, newScriptContent...), suffix...)
}

// rewriteCSS 改写 CSS 内容
func (r *ContentRewriter) rewriteCSS(content []byte, targetDomain string, domainTargets map[string]string) ([]byte, error) {
	// 基础改写
	result := r.cssURLRegex.ReplaceAllFunc(content, func(match []byte) []byte {
		return r.rewriteCSSURL(match, targetDomain, domainTargets)
	})

	return result, nil
}

// rewriteCSSURL 改写 CSS 中的 URL
func (r *ContentRewriter) rewriteCSSURL(match []byte, targetDomain string, domainTargets map[string]string) []byte {
	parts := r.cssURLRegex.FindSubmatch(match)
	if len(parts) != 2 {
		return match
	}

	urlStr := strings.Trim(string(parts[1]), `"'`)
	newURL := r.rewriteURL(urlStr, targetDomain, domainTargets)

	return []byte(fmt.Sprintf("url(%s)", newURL))
}

// rewriteJS 改写 JavaScript 内容
func (r *ContentRewriter) rewriteJS(content []byte, targetDomain string, domainTargets map[string]string) ([]byte, error) {
	result := content

	// 基础改写
	for _, regex := range r.jsURLRegexes {
		result = regex.ReplaceAllFunc(result, func(match []byte) []byte {
			return r.rewriteJSURL(match, targetDomain, domainTargets)
		})
	}

	return result, nil
}

// rewriteJSON 改写 JSON 内容
func (r *ContentRewriter) rewriteJSON(content []byte, targetDomain string, domainTargets map[string]string) ([]byte, error) {
	// 将 JSON 内容作为字符串处理，直接替换 URL
	result := string(content)

	// 改写 JSON 中的 URL 字符串
	for domain, target := range domainTargets {
		// 替换完整的 URL
		result = strings.ReplaceAll(result, fmt.Sprintf("https://%s", domain), fmt.Sprintf("https://%s", target))
		result = strings.ReplaceAll(result, fmt.Sprintf("http://%s", domain), fmt.Sprintf("http://%s", target))
		// 替换域名部分
		result = strings.ReplaceAll(result, fmt.Sprintf(`"%s"`, domain), fmt.Sprintf(`"%s"`, target))
	}

	return []byte(result), nil
}

// rewriteJSURL 改写 JavaScript 中的 URL
func (r *ContentRewriter) rewriteJSURL(match []byte, targetDomain string, domainTargets map[string]string) []byte {
	matchStr := string(match)

	// 提取 URL 部分
	var urlStr string
	if strings.Contains(matchStr, `"`) {
		urlStr = strings.Trim(matchStr, `"`)
	} else if strings.Contains(matchStr, `'`) {
		urlStr = strings.Trim(matchStr, `'`)
	} else {
		return match
	}

	// 改写 URL
	newURL := r.rewriteURL(urlStr, targetDomain, domainTargets)
	return []byte(strings.Replace(matchStr, urlStr, newURL, 1))
}

// rewriteURL 改写 URL
func (r *ContentRewriter) rewriteURL(urlStr, currentDomain string, domainTargets map[string]string) string {
	// 跳过空 URL 和特殊协议
	if urlStr == "" || strings.HasPrefix(urlStr, "data:") || strings.HasPrefix(urlStr, "javascript:") || strings.HasPrefix(urlStr, "mailto:") {
		return urlStr
	}

	// 解析 URL
	parsedURL, err := url.Parse(urlStr)
	if err != nil {
		return urlStr
	}

	// 处理相对 URL
	if parsedURL.Host == "" {
		if strings.HasPrefix(urlStr, "/") {
			// 绝对路径，使用当前域名
			if target, exists := domainTargets[currentDomain]; exists {
				if strings.Contains(target, "localhost:") {
					return fmt.Sprintf("http://%s%s", target, urlStr)
				} else {
					return fmt.Sprintf("https://%s%s", target, urlStr)
				}
			}
		}
		return urlStr
	}

	// 处理绝对 URL
	if target, exists := domainTargets[parsedURL.Host]; exists {
		parsedURL.Host = target
		if strings.Contains(target, "localhost:") {
			parsedURL.Scheme = "http"
		} else {
			parsedURL.Scheme = "https"
		}
		return parsedURL.String()
	}

	return urlStr
}

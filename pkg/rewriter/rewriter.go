package rewriter

import (
	"fmt"
	"net/url"
	"regexp"
	"strings"

	"mirrorgo/pkg/config"
)

// ContentRewriter 内容改写器
type ContentRewriter struct {
	config *config.Config

	// HTML 相关的正则表达式
	htmlAttrRegexes map[string]*regexp.Regexp

	// CSS 相关的正则表达式
	cssURLRegex *regexp.Regexp

	// JavaScript 相关的正则表达式
	jsURLRegexes []*regexp.Regexp

	// 高级改写器
	advancedRewriter *AdvancedRewriter
}

// NewContentRewriter 创建新的内容改写器
func NewContentRewriter(cfg *config.Config) *ContentRewriter {
	rewriter := &ContentRewriter{
		config:           cfg,
		htmlAttrRegexes:  make(map[string]*regexp.Regexp),
		advancedRewriter: NewAdvancedRewriter(),
	}

	// 编译 HTML 属性正则表达式
	for _, attr := range cfg.RewriteConfig.HTMLAttributes {
		pattern := fmt.Sprintf(`(?i)(\s%s\s*=\s*["'])([^"']+)(["'])`, attr)
		rewriter.htmlAttrRegexes[attr] = regexp.MustCompile(pattern)
	}

	// 编译 CSS URL 正则表达式
	rewriter.cssURLRegex = regexp.MustCompile(`(?i)url\s*\(\s*["']?([^"')]+)["']?\s*\)`)

	// 编译 JavaScript URL 正则表达式
	rewriter.jsURLRegexes = []*regexp.Regexp{
		regexp.MustCompile(`(?i)["']https?://[^"']+["']`),
		regexp.MustCompile(`(?i)location\.href\s*=\s*["']([^"']+)["']`),
		regexp.MustCompile(`(?i)window\.open\s*\(\s*["']([^"']+)["']`),
		regexp.MustCompile(`(?i)fetch\s*\(\s*["']([^"']+)["']`),
		regexp.MustCompile(`(?i)XMLHttpRequest.*\.open\s*\(\s*["'][^"']*["']\s*,\s*["']([^"']+)["']`),
	}

	return rewriter
}

// RewriteContent 改写内容
func (r *ContentRewriter) RewriteContent(content []byte, contentType, targetDomain string, domainPorts map[string]int) ([]byte, error) {
	contentType = strings.ToLower(contentType)

	if r.config.RewriteConfig.EnableHTML && strings.Contains(contentType, "text/html") {
		return r.rewriteHTML(content, targetDomain, domainPorts)
	}

	if r.config.RewriteConfig.EnableCSS && strings.Contains(contentType, "text/css") {
		return r.rewriteCSS(content, targetDomain, domainPorts)
	}

	if r.config.RewriteConfig.EnableJS && strings.Contains(contentType, "javascript") {
		return r.rewriteJS(content, targetDomain, domainPorts)
	}

	// 添加 JSON 改写支持（特别是 manifest.json）
	if strings.Contains(contentType, "json") {
		return r.rewriteJSON(content, targetDomain, domainPorts)
	}

	return content, nil
}

// rewriteHTML 改写 HTML 内容
func (r *ContentRewriter) rewriteHTML(content []byte, targetDomain string, domainPorts map[string]int) ([]byte, error) {
	result := content

	// 改写各种 HTML 属性
	for attr, regex := range r.htmlAttrRegexes {
		result = regex.ReplaceAllFunc(result, func(match []byte) []byte {
			return r.rewriteHTMLAttribute(match, attr, targetDomain, domainPorts)
		})
	}

	// 改写内联样式中的 URL
	if r.config.RewriteConfig.EnableCSS {
		styleRegex := regexp.MustCompile(`(?i)(\sstyle\s*=\s*["'])([^"']+)(["'])`)
		result = styleRegex.ReplaceAllFunc(result, func(match []byte) []byte {
			return r.rewriteInlineStyle(match, targetDomain, domainPorts)
		})
	}

	// 改写内联脚本中的 URL
	if r.config.RewriteConfig.EnableJS {
		scriptRegex := regexp.MustCompile(`(?i)(<script[^>]*>)(.*?)(</script>)`)
		result = scriptRegex.ReplaceAllFunc(result, func(match []byte) []byte {
			return r.rewriteInlineScript(match, targetDomain, domainPorts)
		})
	}

	return result, nil
}

// rewriteHTMLAttribute 改写 HTML 属性
func (r *ContentRewriter) rewriteHTMLAttribute(match []byte, attr, targetDomain string, domainPorts map[string]int) []byte {
	parts := r.htmlAttrRegexes[attr].FindSubmatch(match)
	if len(parts) != 4 {
		return match
	}

	prefix := parts[1]         // 属性名和等号
	urlStr := string(parts[2]) // URL 值
	suffix := parts[3]         // 引号

	// 改写 URL
	newURL := r.rewriteURL(urlStr, targetDomain, domainPorts)

	return append(append(prefix, []byte(newURL)...), suffix...)
}

// rewriteInlineStyle 改写内联样式
func (r *ContentRewriter) rewriteInlineStyle(match []byte, targetDomain string, domainPorts map[string]int) []byte {
	styleRegex := regexp.MustCompile(`(?i)(\sstyle\s*=\s*["'])([^"']+)(["'])`)
	parts := styleRegex.FindSubmatch(match)
	if len(parts) != 4 {
		return match
	}

	prefix := parts[1]
	styleContent := parts[2]
	suffix := parts[3]

	// 改写样式中的 URL
	newStyleContent := r.cssURLRegex.ReplaceAllFunc(styleContent, func(urlMatch []byte) []byte {
		return r.rewriteCSSURL(urlMatch, targetDomain, domainPorts)
	})

	return append(append(prefix, newStyleContent...), suffix...)
}

// rewriteInlineScript 改写内联脚本
func (r *ContentRewriter) rewriteInlineScript(match []byte, targetDomain string, domainPorts map[string]int) []byte {
	scriptRegex := regexp.MustCompile(`(?i)(<script[^>]*>)(.*?)(</script>)`)
	parts := scriptRegex.FindSubmatch(match)
	if len(parts) != 4 {
		return match
	}

	prefix := parts[1]
	scriptContent := parts[2]
	suffix := parts[3]

	// 改写脚本中的 URL
	newScriptContent := scriptContent
	for _, regex := range r.jsURLRegexes {
		newScriptContent = regex.ReplaceAllFunc(newScriptContent, func(urlMatch []byte) []byte {
			return r.rewriteJSURL(urlMatch, targetDomain, domainPorts)
		})
	}

	return append(append(prefix, newScriptContent...), suffix...)
}

// rewriteCSS 改写 CSS 内容
func (r *ContentRewriter) rewriteCSS(content []byte, targetDomain string, domainPorts map[string]int) ([]byte, error) {
	// 基础改写
	result := r.cssURLRegex.ReplaceAllFunc(content, func(match []byte) []byte {
		return r.rewriteCSSURL(match, targetDomain, domainPorts)
	})

	// 高级改写
	result = r.advancedRewriter.RewriteAdvancedCSS(result, targetDomain, domainPorts, r)

	return result, nil
}

// rewriteCSSURL 改写 CSS 中的 URL
func (r *ContentRewriter) rewriteCSSURL(match []byte, targetDomain string, domainPorts map[string]int) []byte {
	parts := r.cssURLRegex.FindSubmatch(match)
	if len(parts) != 2 {
		return match
	}

	urlStr := string(parts[1])
	newURL := r.rewriteURL(urlStr, targetDomain, domainPorts)

	return []byte(fmt.Sprintf("url(%s)", newURL))
}

// rewriteJS 改写 JavaScript 内容
func (r *ContentRewriter) rewriteJS(content []byte, targetDomain string, domainPorts map[string]int) ([]byte, error) {
	result := content

	// 基础改写
	for _, regex := range r.jsURLRegexes {
		result = regex.ReplaceAllFunc(result, func(match []byte) []byte {
			return r.rewriteJSURL(match, targetDomain, domainPorts)
		})
	}

	// 高级改写
	result = r.advancedRewriter.RewriteAdvancedJS(result, targetDomain, domainPorts, r)

	return result, nil
}

// rewriteJSON 改写 JSON 内容
func (r *ContentRewriter) rewriteJSON(content []byte, targetDomain string, domainPorts map[string]int) ([]byte, error) {
	// 将 JSON 内容作为字符串处理，直接替换 URL
	result := string(content)

	// 改写 JSON 中的 URL 字符串
	for domain, port := range domainPorts {
		// 替换 https:// 和 http:// 的完整 URL
		httpsURL := fmt.Sprintf("https://%s", domain)
		httpURL := fmt.Sprintf("http://%s", domain)
		localURL := fmt.Sprintf("http://localhost:%d", port)

		result = strings.ReplaceAll(result, httpsURL, localURL)
		result = strings.ReplaceAll(result, httpURL, localURL)
	}

	return []byte(result), nil
}

// rewriteJSURL 改写 JavaScript 中的 URL
func (r *ContentRewriter) rewriteJSURL(match []byte, targetDomain string, domainPorts map[string]int) []byte {
	matchStr := string(match)

	// 提取 URL 部分
	var urlStr string
	if strings.Contains(matchStr, `"`) {
		parts := strings.Split(matchStr, `"`)
		if len(parts) >= 3 {
			urlStr = parts[1]
		}
	} else if strings.Contains(matchStr, `'`) {
		parts := strings.Split(matchStr, `'`)
		if len(parts) >= 3 {
			urlStr = parts[1]
		}
	}

	if urlStr == "" {
		return match
	}

	newURL := r.rewriteURL(urlStr, targetDomain, domainPorts)
	return []byte(strings.Replace(matchStr, urlStr, newURL, 1))
}

// rewriteURL 改写 URL
func (r *ContentRewriter) rewriteURL(urlStr, currentDomain string, domainPorts map[string]int) string {
	// 跳过空 URL 和特殊协议
	if urlStr == "" || strings.HasPrefix(urlStr, "data:") || strings.HasPrefix(urlStr, "javascript:") || strings.HasPrefix(urlStr, "mailto:") {
		return urlStr
	}

	// 解析 URL
	parsedURL, err := url.Parse(urlStr)
	if err != nil {
		return urlStr
	}

	// 处理相对 URL
	if parsedURL.Host == "" {
		if strings.HasPrefix(urlStr, "//") {
			// 协议相对 URL
			parsedURL.Scheme = "https"
			parsedURL.Host = strings.TrimPrefix(urlStr, "//")
			if idx := strings.Index(parsedURL.Host, "/"); idx != -1 {
				parsedURL.Path = parsedURL.Host[idx:]
				parsedURL.Host = parsedURL.Host[:idx]
			}
		} else if strings.HasPrefix(urlStr, "/") {
			// 绝对路径
			if port, exists := domainPorts[currentDomain]; exists {
				return fmt.Sprintf("http://localhost:%d%s", port, urlStr)
			}
			return urlStr
		} else {
			// 相对路径，保持不变
			return urlStr
		}
	}

	// 检查是否需要代理这个域名
	if port, exists := domainPorts[parsedURL.Host]; exists {
		// 构建本地代理 URL
		localURL := fmt.Sprintf("http://localhost:%d%s", port, parsedURL.Path)
		if parsedURL.RawQuery != "" {
			localURL += "?" + parsedURL.RawQuery
		}
		if parsedURL.Fragment != "" {
			localURL += "#" + parsedURL.Fragment
		}
		return localURL
	}

	// 对于未配置的域名，保持原始 URL 不变
	// 这符合多端口架构的设计理念：只代理明确配置的域名
	return urlStr
}

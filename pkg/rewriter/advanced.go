package rewriter

import (
	"regexp"
	"strings"
)

// AdvancedRewriter 高级内容改写器
type AdvancedRewriter struct {
	// JavaScript 高级正则表达式
	jsAdvancedRegexes []*regexp.Regexp

	// CSS 高级正则表达式
	cssAdvancedRegexes []*regexp.Regexp

	// 常见的 JavaScript 框架和库的 URL 模式
	jsFrameworkPatterns []*regexp.Regexp
}

// NewAdvancedRewriter 创建高级改写器
func NewAdvancedRewriter() *AdvancedRewriter {
	rewriter := &AdvancedRewriter{}

	// JavaScript 高级模式
	rewriter.jsAdvancedRegexes = []*regexp.Regexp{
		// Ajax 请求
		regexp.MustCompile(`(?i)\$\.ajax\s*\(\s*\{[^}]*url\s*:\s*["']([^"']+)["']`),
		regexp.MustCompile(`(?i)\$\.get\s*\(\s*["']([^"']+)["']`),
		regexp.MustCompile(`(?i)\$\.post\s*\(\s*["']([^"']+)["']`),
		regexp.MustCompile(`(?i)\$\.load\s*\(\s*["']([^"']+)["']`),

		// Fetch API
		regexp.MustCompile(`(?i)fetch\s*\(\s*["']([^"']+)["']`),
		regexp.MustCompile("(?i)fetch\\s*\\(\\s*`([^`]+)`"),

		// XMLHttpRequest
		regexp.MustCompile(`(?i)\.open\s*\(\s*["'][^"']*["']\s*,\s*["']([^"']+)["']`),

		// WebSocket
		regexp.MustCompile(`(?i)new\s+WebSocket\s*\(\s*["']([^"']+)["']`),

		// 动态脚本加载
		regexp.MustCompile(`(?i)\.src\s*=\s*["']([^"']+)["']`),

		// 图片预加载
		regexp.MustCompile(`(?i)new\s+Image\s*\(\s*\).*\.src\s*=\s*["']([^"']+)["']`),

		// CSS 动态加载
		regexp.MustCompile(`(?i)\.href\s*=\s*["']([^"']+\.css[^"']*)["']`),

		// 模板字符串中的 URL
		regexp.MustCompile("(?i)`[^`]*https?://[^`]*`"),

		// JSON 中的 URL
		regexp.MustCompile(`(?i)["']url["']\s*:\s*["']([^"']+)["']`),
		regexp.MustCompile(`(?i)["']src["']\s*:\s*["']([^"']+)["']`),
		regexp.MustCompile(`(?i)["']href["']\s*:\s*["']([^"']+)["']`),
	}

	// CSS 高级模式
	rewriter.cssAdvancedRegexes = []*regexp.Regexp{
		// @import 规则
		regexp.MustCompile(`(?i)@import\s+url\s*\(\s*["']?([^"')]+)["']?\s*\)`),
		regexp.MustCompile(`(?i)@import\s+["']([^"']+)["']`),

		// 字体文件
		regexp.MustCompile(`(?i)@font-face\s*\{[^}]*src\s*:[^}]*url\s*\(\s*["']?([^"')]+)["']?\s*\)`),

		// 背景图片的各种写法
		regexp.MustCompile(`(?i)background\s*:\s*[^;]*url\s*\(\s*["']?([^"')]+)["']?\s*\)`),
		regexp.MustCompile(`(?i)background-image\s*:\s*url\s*\(\s*["']?([^"')]+)["']?\s*\)`),

		// CSS 变量中的 URL
		regexp.MustCompile(`(?i)--[^:]*:\s*url\s*\(\s*["']?([^"')]+)["']?\s*\)`),

		// 媒体查询中的 URL
		regexp.MustCompile(`(?i)@media[^{]*\{[^}]*url\s*\(\s*["']?([^"')]+)["']?\s*\)`),
	}

	// JavaScript 框架特定模式
	rewriter.jsFrameworkPatterns = []*regexp.Regexp{
		// React/Vue 等框架的资源引用
		regexp.MustCompile(`(?i)require\s*\(\s*["']([^"']+)["']\s*\)`),
		regexp.MustCompile(`(?i)import\s+[^"']*["']([^"']+)["']`),

		// Angular 的模板 URL
		regexp.MustCompile(`(?i)templateUrl\s*:\s*["']([^"']+)["']`),
		regexp.MustCompile(`(?i)styleUrls\s*:\s*\[\s*["']([^"']+)["']`),

		// Vue 的组件引用
		regexp.MustCompile(`(?i)component\s*:\s*\(\s*\)\s*=>\s*import\s*\(\s*["']([^"']+)["']`),
	}

	return rewriter
}

// RewriteAdvancedJS 高级 JavaScript 改写
func (ar *AdvancedRewriter) RewriteAdvancedJS(content []byte, targetDomain string, domainPorts map[string]int, baseRewriter *ContentRewriter) []byte {
	result := content

	// 应用高级 JavaScript 模式
	for _, regex := range ar.jsAdvancedRegexes {
		result = regex.ReplaceAllFunc(result, func(match []byte) []byte {
			return ar.rewriteAdvancedJSMatch(match, regex, targetDomain, domainPorts, baseRewriter)
		})
	}

	// 应用框架特定模式
	for _, regex := range ar.jsFrameworkPatterns {
		result = regex.ReplaceAllFunc(result, func(match []byte) []byte {
			return ar.rewriteFrameworkMatch(match, regex, targetDomain, domainPorts, baseRewriter)
		})
	}

	return result
}

// RewriteAdvancedCSS 高级 CSS 改写
func (ar *AdvancedRewriter) RewriteAdvancedCSS(content []byte, targetDomain string, domainPorts map[string]int, baseRewriter *ContentRewriter) []byte {
	result := content

	// 应用高级 CSS 模式
	for _, regex := range ar.cssAdvancedRegexes {
		result = regex.ReplaceAllFunc(result, func(match []byte) []byte {
			return ar.rewriteAdvancedCSSMatch(match, regex, targetDomain, domainPorts, baseRewriter)
		})
	}

	return result
}

// rewriteAdvancedJSMatch 改写高级 JavaScript 匹配
func (ar *AdvancedRewriter) rewriteAdvancedJSMatch(match []byte, regex *regexp.Regexp, targetDomain string, domainPorts map[string]int, baseRewriter *ContentRewriter) []byte {
	parts := regex.FindSubmatch(match)
	if len(parts) < 2 {
		return match
	}

	matchStr := string(match)
	urlStr := string(parts[1])

	// 使用基础改写器改写 URL
	newURL := baseRewriter.rewriteURL(urlStr, targetDomain, domainPorts)

	// 替换原始 URL
	return []byte(strings.Replace(matchStr, urlStr, newURL, 1))
}

// rewriteAdvancedCSSMatch 改写高级 CSS 匹配
func (ar *AdvancedRewriter) rewriteAdvancedCSSMatch(match []byte, regex *regexp.Regexp, targetDomain string, domainPorts map[string]int, baseRewriter *ContentRewriter) []byte {
	parts := regex.FindSubmatch(match)
	if len(parts) < 2 {
		return match
	}

	matchStr := string(match)
	urlStr := string(parts[1])

	// 使用基础改写器改写 URL
	newURL := baseRewriter.rewriteURL(urlStr, targetDomain, domainPorts)

	// 替换原始 URL
	return []byte(strings.Replace(matchStr, urlStr, newURL, 1))
}

// rewriteFrameworkMatch 改写框架特定匹配
func (ar *AdvancedRewriter) rewriteFrameworkMatch(match []byte, regex *regexp.Regexp, targetDomain string, domainPorts map[string]int, baseRewriter *ContentRewriter) []byte {
	parts := regex.FindSubmatch(match)
	if len(parts) < 2 {
		return match
	}

	matchStr := string(match)
	urlStr := string(parts[1])

	// 对于模块导入，只改写 HTTP/HTTPS URL
	if strings.HasPrefix(urlStr, "http://") || strings.HasPrefix(urlStr, "https://") {
		newURL := baseRewriter.rewriteURL(urlStr, targetDomain, domainPorts)
		return []byte(strings.Replace(matchStr, urlStr, newURL, 1))
	}

	// 对于相对路径的模块导入，保持不变
	return match
}

// DetectContentType 检测内容类型的更精确方法
func (ar *AdvancedRewriter) DetectContentType(content []byte, declaredType string) string {
	contentStr := string(content)

	// 检测 JavaScript
	if strings.Contains(declaredType, "javascript") ||
		strings.Contains(contentStr, "function") ||
		strings.Contains(contentStr, "var ") ||
		strings.Contains(contentStr, "let ") ||
		strings.Contains(contentStr, "const ") ||
		strings.Contains(contentStr, "=>") {
		return "javascript"
	}

	// 检测 CSS
	if strings.Contains(declaredType, "css") ||
		strings.Contains(contentStr, "{") && strings.Contains(contentStr, "}") &&
			(strings.Contains(contentStr, "color:") || strings.Contains(contentStr, "background:") || strings.Contains(contentStr, "font-")) {
		return "css"
	}

	// 检测 HTML
	if strings.Contains(declaredType, "html") ||
		strings.Contains(contentStr, "<html") ||
		strings.Contains(contentStr, "<!DOCTYPE") ||
		strings.Contains(contentStr, "<head>") ||
		strings.Contains(contentStr, "<body>") {
		return "html"
	}

	// 检测 JSON
	if strings.Contains(declaredType, "json") ||
		(strings.HasPrefix(strings.TrimSpace(contentStr), "{") && strings.HasSuffix(strings.TrimSpace(contentStr), "}")) ||
		(strings.HasPrefix(strings.TrimSpace(contentStr), "[") && strings.HasSuffix(strings.TrimSpace(contentStr), "]")) {
		return "json"
	}

	return "unknown"
}

// ShouldRewriteURL 判断是否应该改写特定的 URL
func (ar *AdvancedRewriter) ShouldRewriteURL(urlStr string) bool {
	// 跳过特殊协议
	skipProtocols := []string{"data:", "javascript:", "mailto:", "tel:", "ftp:", "file:"}
	for _, protocol := range skipProtocols {
		if strings.HasPrefix(strings.ToLower(urlStr), protocol) {
			return false
		}
	}

	// 跳过锚点链接
	if strings.HasPrefix(urlStr, "#") {
		return false
	}

	// 跳过空 URL
	if strings.TrimSpace(urlStr) == "" {
		return false
	}

	return true
}

package rewriter

import (
	"strings"
	"testing"

	"mirrorgo/pkg/config"
)

func TestRewriteHTML(t *testing.T) {
	cfg := &config.Config{
		RewriteConfig: &config.RewriteConfig{
			EnableHTML:     true,
			HTMLAttributes: []string{"src", "href", "action"},
		},
	}

	rewriter := NewContentRewriter(cfg)
	domainPorts := map[string]int{
		"github.com":  8080,
		"example.com": 8081,
	}

	tests := []struct {
		name     string
		input    string
		expected string
	}{
		{
			name:     "改写 img src",
			input:    `<img src="https://github.com/avatar.png">`,
			expected: `<img src="http://localhost:8080/avatar.png">`,
		},
		{
			name:     "改写 a href",
			input:    `<a href="https://example.com/page">Link</a>`,
			expected: `<a href="http://localhost:8081/page">Link</a>`,
		},
		{
			name:     "改写 form action",
			input:    `<form action="https://github.com/login">`,
			expected: `<form action="http://localhost:8080/login">`,
		},
		{
			name:     "保持相对路径不变",
			input:    `<a href="/relative/path">Link</a>`,
			expected: `<a href="http://localhost:8080/relative/path">Link</a>`,
		},
		{
			name:     "未配置域名使用代理路径",
			input:    `<img src="https://unknown.com/image.png">`,
			expected: `<img src="/proxy/https/unknown.com/image.png">`,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := rewriter.rewriteHTML([]byte(tt.input), "github.com", domainPorts)
			if err != nil {
				t.Fatalf("改写 HTML 失败: %v", err)
			}

			resultStr := string(result)
			if resultStr != tt.expected {
				t.Errorf("期望: %s\n实际: %s", tt.expected, resultStr)
			}
		})
	}
}

func TestRewriteCSS(t *testing.T) {
	cfg := &config.Config{
		RewriteConfig: &config.RewriteConfig{
			EnableCSS: true,
		},
	}

	rewriter := NewContentRewriter(cfg)
	domainPorts := map[string]int{
		"fonts.googleapis.com": 8080,
	}

	tests := []struct {
		name     string
		input    string
		expected string
	}{
		{
			name:     "改写 CSS url()",
			input:    `background: url(https://fonts.googleapis.com/font.woff);`,
			expected: `background: url(http://localhost:8080/font.woff);`,
		},
		{
			name:     "改写带引号的 URL",
			input:    `background-image: url("https://fonts.googleapis.com/bg.png");`,
			expected: `background-image: url(http://localhost:8080/bg.png);`,
		},
		{
			name:     "未配置域名使用代理路径",
			input:    `@import url(https://unknown.com/style.css);`,
			expected: `@import url(/proxy/https/unknown.com/style.css);`,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := rewriter.rewriteCSS([]byte(tt.input), "example.com", domainPorts)
			if err != nil {
				t.Fatalf("改写 CSS 失败: %v", err)
			}

			resultStr := string(result)
			// 检查是否包含改写后的内容（localhost 或 proxy 路径）
			if !strings.Contains(resultStr, "localhost:8080") && !strings.Contains(resultStr, "/proxy/") {
				t.Errorf("期望包含改写后的内容\n期望包含: localhost:8080 或 /proxy/\n实际: %s", resultStr)
			}
		})
	}
}

func TestRewriteJS(t *testing.T) {
	cfg := &config.Config{
		RewriteConfig: &config.RewriteConfig{
			EnableJS: true,
		},
	}

	rewriter := NewContentRewriter(cfg)
	domainPorts := map[string]int{
		"api.github.com": 8080,
	}

	tests := []struct {
		name     string
		input    string
		expected string
	}{
		{
			name:     "改写 fetch URL",
			input:    `fetch("https://api.github.com/user")`,
			expected: `fetch("http://localhost:8080/user")`,
		},
		{
			name:     "改写 location.href",
			input:    `location.href = "https://api.github.com/redirect"`,
			expected: `location.href = "http://localhost:8080/redirect"`,
		},
		{
			name:     "未配置域名使用代理路径",
			input:    `fetch("https://unknown-api.com/data")`,
			expected: `fetch("/proxy/https/unknown-api.com/data")`,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := rewriter.rewriteJS([]byte(tt.input), "github.com", domainPorts)
			if err != nil {
				t.Fatalf("改写 JS 失败: %v", err)
			}

			resultStr := string(result)
			// 检查是否包含改写后的内容（localhost 或 proxy 路径）
			if !strings.Contains(resultStr, "localhost:8080") && !strings.Contains(resultStr, "/proxy/") {
				t.Errorf("期望包含改写后的内容\n期望包含: localhost:8080 或 /proxy/\n实际: %s", resultStr)
			}
		})
	}
}

func TestRewriteURL(t *testing.T) {
	cfg := &config.Config{
		RewriteConfig: &config.RewriteConfig{
			EnableHTML: true,
		},
	}

	rewriter := NewContentRewriter(cfg)
	domainPorts := map[string]int{
		"github.com":  8080,
		"example.com": 8081,
	}

	tests := []struct {
		name          string
		url           string
		currentDomain string
		expected      string
	}{
		{
			name:          "绝对 HTTPS URL",
			url:           "https://github.com/user/repo",
			currentDomain: "example.com",
			expected:      "http://localhost:8080/user/repo",
		},
		{
			name:          "绝对 HTTP URL",
			url:           "http://example.com/page",
			currentDomain: "github.com",
			expected:      "http://localhost:8081/page",
		},
		{
			name:          "相对路径",
			url:           "/api/data",
			currentDomain: "github.com",
			expected:      "http://localhost:8080/api/data",
		},
		{
			name:          "协议相对 URL",
			url:           "//github.com/script.js",
			currentDomain: "example.com",
			expected:      "http://localhost:8080/script.js",
		},
		{
			name:          "未配置域名",
			url:           "https://unknown.com/resource",
			currentDomain: "github.com",
			expected:      "/proxy/https/unknown.com/resource",
		},
		{
			name:          "跳过 data URL",
			url:           "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8/5+hHgAHggJ/PchI7wAAAABJRU5ErkJggg==",
			currentDomain: "github.com",
			expected:      "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8/5+hHgAHggJ/PchI7wAAAABJRU5ErkJggg==",
		},
		{
			name:          "跳过 javascript URL",
			url:           "javascript:void(0)",
			currentDomain: "github.com",
			expected:      "javascript:void(0)",
		},
		{
			name:          "跳过 mailto URL",
			url:           "mailto:<EMAIL>",
			currentDomain: "github.com",
			expected:      "mailto:<EMAIL>",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := rewriter.rewriteURL(tt.url, tt.currentDomain, domainPorts)
			if result != tt.expected {
				t.Errorf("期望: %s\n实际: %s", tt.expected, result)
			}
		})
	}
}

func TestRewriteContent(t *testing.T) {
	cfg := &config.Config{
		RewriteConfig: &config.RewriteConfig{
			EnableHTML:     true,
			EnableCSS:      true,
			EnableJS:       true,
			HTMLAttributes: []string{"src", "href"},
		},
	}

	rewriter := NewContentRewriter(cfg)
	domainPorts := map[string]int{
		"github.com": 8080,
	}

	tests := []struct {
		name        string
		content     string
		contentType string
		expected    string
	}{
		{
			name:        "HTML 内容",
			content:     `<a href="https://github.com/repo">Link</a>`,
			contentType: "text/html",
			expected:    `<a href="http://localhost:8080/repo">Link</a>`,
		},
		{
			name:        "CSS 内容",
			content:     `background: url(https://github.com/bg.png);`,
			contentType: "text/css",
			expected:    `background: url(http://localhost:8080/bg.png);`,
		},
		{
			name:        "JavaScript 内容",
			content:     `fetch("https://github.com/api")`,
			contentType: "application/javascript",
			expected:    `fetch("http://localhost:8080/api")`,
		},
		{
			name:        "不支持的内容类型",
			content:     `plain text content`,
			contentType: "text/plain",
			expected:    `plain text content`,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := rewriter.RewriteContent([]byte(tt.content), tt.contentType, "example.com", domainPorts)
			if err != nil {
				t.Fatalf("改写内容失败: %v", err)
			}

			resultStr := string(result)
			// 对于不支持的内容类型，应该保持原样
			if tt.contentType == "text/plain" {
				if resultStr != tt.expected {
					t.Errorf("期望: %s\n实际: %s", tt.expected, resultStr)
				}
			} else {
				// 对于支持的内容类型，检查是否包含改写后的内容
				if !strings.Contains(resultStr, "localhost:8080") && !strings.Contains(resultStr, "/proxy/") {
					t.Errorf("期望包含改写后的内容\n期望包含: localhost:8080 或 /proxy/\n实际: %s", resultStr)
				}
			}
		})
	}
}

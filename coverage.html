
<!DOCTYPE html>
<html>
	<head>
		<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
		<title>config: Go Coverage Report</title>
		<style>
			body {
				background: black;
				color: rgb(80, 80, 80);
			}
			body, pre, #legend span {
				font-family: Menlo, monospace;
				font-weight: bold;
			}
			#topbar {
				background: black;
				position: fixed;
				top: 0; left: 0; right: 0;
				height: 42px;
				border-bottom: 1px solid rgb(80, 80, 80);
			}
			#content {
				margin-top: 50px;
			}
			#nav, #legend {
				float: left;
				margin-left: 10px;
			}
			#legend {
				margin-top: 12px;
			}
			#nav {
				margin-top: 10px;
			}
			#legend span {
				margin: 0 5px;
			}
			.cov0 { color: rgb(192, 0, 0) }
.cov1 { color: rgb(128, 128, 128) }
.cov2 { color: rgb(116, 140, 131) }
.cov3 { color: rgb(104, 152, 134) }
.cov4 { color: rgb(92, 164, 137) }
.cov5 { color: rgb(80, 176, 140) }
.cov6 { color: rgb(68, 188, 143) }
.cov7 { color: rgb(56, 200, 146) }
.cov8 { color: rgb(44, 212, 149) }
.cov9 { color: rgb(32, 224, 152) }
.cov10 { color: rgb(20, 236, 155) }

		</style>
	</head>
	<body>
		<div id="topbar">
			<div id="nav">
				<select id="files">
				
				<option value="file0">mirrorgo/pkg/config/config.go (88.4%)</option>
				
				<option value="file1">mirrorgo/pkg/rewriter/advanced.go (48.3%)</option>
				
				<option value="file2">mirrorgo/pkg/rewriter/rewriter.go (64.5%)</option>
				
				</select>
			</div>
			<div id="legend">
				<span>not tracked</span>
			
				<span class="cov0">not covered</span>
				<span class="cov8">covered</span>
			
			</div>
		</div>
		<div id="content">
		
		<pre class="file" id="file0" style="display: none">package config

import (
        "fmt"
        "io/ioutil"
        "net/url"

        "gopkg.in/yaml.v3"
)

// Config 代理服务器配置
type Config struct {
        // DomainPorts 域名到端口的映射
        DomainPorts map[string]int `yaml:"domain_ports"`
        
        // ProxyConfig 可选的 HTTP 代理配置
        ProxyConfig *ProxyConfig `yaml:"proxy,omitempty"`
        
        // RewriteConfig 内容改写配置
        RewriteConfig *RewriteConfig `yaml:"rewrite,omitempty"`
        
        // ServerConfig 服务器配置
        ServerConfig *ServerConfig `yaml:"server,omitempty"`
}

// ProxyConfig HTTP 代理配置
type ProxyConfig struct {
        // Enable 是否启用代理
        Enable bool `yaml:"enable"`
        
        // URL 代理服务器地址 (例如: http://proxy.example.com:8080)
        URL string `yaml:"url"`
        
        // Username 代理用户名 (可选)
        Username string `yaml:"username,omitempty"`
        
        // Password 代理密码 (可选)
        Password string `yaml:"password,omitempty"`
}

// RewriteConfig 内容改写配置
type RewriteConfig struct {
        // EnableHTML 是否启用 HTML 改写
        EnableHTML bool `yaml:"enable_html"`
        
        // EnableJS 是否启用 JavaScript 改写
        EnableJS bool `yaml:"enable_js"`
        
        // EnableCSS 是否启用 CSS 改写
        EnableCSS bool `yaml:"enable_css"`
        
        // HTMLAttributes 需要改写的 HTML 属性列表
        HTMLAttributes []string `yaml:"html_attributes,omitempty"`
}

// ServerConfig 服务器配置
type ServerConfig struct {
        // ReadTimeout 读取超时时间 (秒)
        ReadTimeout int `yaml:"read_timeout"`
        
        // WriteTimeout 写入超时时间 (秒)
        WriteTimeout int `yaml:"write_timeout"`
        
        // IdleTimeout 空闲超时时间 (秒)
        IdleTimeout int `yaml:"idle_timeout"`
}

// LoadConfig 从文件加载配置
func LoadConfig(path string) (*Config, error) <span class="cov8" title="1">{
        data, err := ioutil.ReadFile(path)
        if err != nil </span><span class="cov0" title="0">{
                return nil, fmt.Errorf("读取配置文件失败: %w", err)
        }</span>

        <span class="cov8" title="1">var config Config
        if err := yaml.Unmarshal(data, &amp;config); err != nil </span><span class="cov0" title="0">{
                return nil, fmt.Errorf("解析配置文件失败: %w", err)
        }</span>

        // 设置默认值
        <span class="cov8" title="1">if err := config.setDefaults(); err != nil </span><span class="cov0" title="0">{
                return nil, fmt.Errorf("设置默认配置失败: %w", err)
        }</span>

        // 验证配置
        <span class="cov8" title="1">if err := config.validate(); err != nil </span><span class="cov8" title="1">{
                return nil, fmt.Errorf("配置验证失败: %w", err)
        }</span>

        <span class="cov8" title="1">return &amp;config, nil</span>
}

// setDefaults 设置默认配置值
func (c *Config) setDefaults() error <span class="cov8" title="1">{
        // 设置默认的改写配置
        if c.RewriteConfig == nil </span><span class="cov8" title="1">{
                c.RewriteConfig = &amp;RewriteConfig{}
        }</span>
        
        <span class="cov8" title="1">if c.RewriteConfig.HTMLAttributes == nil </span><span class="cov8" title="1">{
                c.RewriteConfig.HTMLAttributes = []string{"src", "href", "action", "data-src", "data-href"}
        }</span>
        
        // 默认启用 HTML 改写
        <span class="cov8" title="1">if !c.RewriteConfig.EnableHTML &amp;&amp; !c.RewriteConfig.EnableJS &amp;&amp; !c.RewriteConfig.EnableCSS </span><span class="cov8" title="1">{
                c.RewriteConfig.EnableHTML = true
                c.RewriteConfig.EnableJS = true
                c.RewriteConfig.EnableCSS = true
        }</span>

        // 设置默认的服务器配置
        <span class="cov8" title="1">if c.ServerConfig == nil </span><span class="cov8" title="1">{
                c.ServerConfig = &amp;ServerConfig{
                        ReadTimeout:  30,
                        WriteTimeout: 30,
                        IdleTimeout:  60,
                }
        }</span>

        <span class="cov8" title="1">return nil</span>
}

// validate 验证配置
func (c *Config) validate() error <span class="cov8" title="1">{
        if len(c.DomainPorts) == 0 </span><span class="cov8" title="1">{
                return fmt.Errorf("至少需要配置一个域名端口映射")
        }</span>

        // 验证端口范围
        <span class="cov8" title="1">for domain, port := range c.DomainPorts </span><span class="cov8" title="1">{
                if port &lt; 1 || port &gt; 65535 </span><span class="cov8" title="1">{
                        return fmt.Errorf("域名 %s 的端口 %d 超出有效范围 (1-65535)", domain, port)
                }</span>
        }

        // 验证代理配置
        <span class="cov8" title="1">if c.ProxyConfig != nil &amp;&amp; c.ProxyConfig.Enable </span><span class="cov8" title="1">{
                if c.ProxyConfig.URL == "" </span><span class="cov8" title="1">{
                        return fmt.Errorf("启用代理时必须提供代理 URL")
                }</span>
                
                <span class="cov8" title="1">if _, err := url.Parse(c.ProxyConfig.URL); err != nil </span><span class="cov8" title="1">{
                        return fmt.Errorf("代理 URL 格式无效: %w", err)
                }</span>
        }

        <span class="cov8" title="1">return nil</span>
}

// GetProxyURL 获取代理 URL
func (c *Config) GetProxyURL() (*url.URL, error) <span class="cov8" title="1">{
        if c.ProxyConfig == nil || !c.ProxyConfig.Enable </span><span class="cov8" title="1">{
                return nil, nil
        }</span>

        <span class="cov8" title="1">proxyURL, err := url.Parse(c.ProxyConfig.URL)
        if err != nil </span><span class="cov0" title="0">{
                return nil, err
        }</span>

        // 设置认证信息
        <span class="cov8" title="1">if c.ProxyConfig.Username != "" </span><span class="cov8" title="1">{
                if c.ProxyConfig.Password != "" </span><span class="cov8" title="1">{
                        proxyURL.User = url.UserPassword(c.ProxyConfig.Username, c.ProxyConfig.Password)
                }</span> else<span class="cov0" title="0"> {
                        proxyURL.User = url.User(c.ProxyConfig.Username)
                }</span>
        }

        <span class="cov8" title="1">return proxyURL, nil</span>
}
</pre>
		
		<pre class="file" id="file1" style="display: none">package rewriter

import (
        "regexp"
        "strings"
)

// AdvancedRewriter 高级内容改写器
type AdvancedRewriter struct {
        // JavaScript 高级正则表达式
        jsAdvancedRegexes []*regexp.Regexp

        // CSS 高级正则表达式
        cssAdvancedRegexes []*regexp.Regexp

        // 常见的 JavaScript 框架和库的 URL 模式
        jsFrameworkPatterns []*regexp.Regexp
}

// NewAdvancedRewriter 创建高级改写器
func NewAdvancedRewriter() *AdvancedRewriter <span class="cov8" title="1">{
        rewriter := &amp;AdvancedRewriter{}

        // JavaScript 高级模式
        rewriter.jsAdvancedRegexes = []*regexp.Regexp{
                // Ajax 请求
                regexp.MustCompile(`(?i)\$\.ajax\s*\(\s*\{[^}]*url\s*:\s*["']([^"']+)["']`),
                regexp.MustCompile(`(?i)\$\.get\s*\(\s*["']([^"']+)["']`),
                regexp.MustCompile(`(?i)\$\.post\s*\(\s*["']([^"']+)["']`),
                regexp.MustCompile(`(?i)\$\.load\s*\(\s*["']([^"']+)["']`),

                // Fetch API
                regexp.MustCompile(`(?i)fetch\s*\(\s*["']([^"']+)["']`),
                regexp.MustCompile("(?i)fetch\\s*\\(\\s*`([^`]+)`"),

                // XMLHttpRequest
                regexp.MustCompile(`(?i)\.open\s*\(\s*["'][^"']*["']\s*,\s*["']([^"']+)["']`),

                // WebSocket
                regexp.MustCompile(`(?i)new\s+WebSocket\s*\(\s*["']([^"']+)["']`),

                // 动态脚本加载
                regexp.MustCompile(`(?i)\.src\s*=\s*["']([^"']+)["']`),

                // 图片预加载
                regexp.MustCompile(`(?i)new\s+Image\s*\(\s*\).*\.src\s*=\s*["']([^"']+)["']`),

                // CSS 动态加载
                regexp.MustCompile(`(?i)\.href\s*=\s*["']([^"']+\.css[^"']*)["']`),

                // 模板字符串中的 URL
                regexp.MustCompile("(?i)`[^`]*https?://[^`]*`"),

                // JSON 中的 URL
                regexp.MustCompile(`(?i)["']url["']\s*:\s*["']([^"']+)["']`),
                regexp.MustCompile(`(?i)["']src["']\s*:\s*["']([^"']+)["']`),
                regexp.MustCompile(`(?i)["']href["']\s*:\s*["']([^"']+)["']`),
        }

        // CSS 高级模式
        rewriter.cssAdvancedRegexes = []*regexp.Regexp{
                // @import 规则
                regexp.MustCompile(`(?i)@import\s+url\s*\(\s*["']?([^"')]+)["']?\s*\)`),
                regexp.MustCompile(`(?i)@import\s+["']([^"']+)["']`),

                // 字体文件
                regexp.MustCompile(`(?i)@font-face\s*\{[^}]*src\s*:[^}]*url\s*\(\s*["']?([^"')]+)["']?\s*\)`),

                // 背景图片的各种写法
                regexp.MustCompile(`(?i)background\s*:\s*[^;]*url\s*\(\s*["']?([^"')]+)["']?\s*\)`),
                regexp.MustCompile(`(?i)background-image\s*:\s*url\s*\(\s*["']?([^"')]+)["']?\s*\)`),

                // CSS 变量中的 URL
                regexp.MustCompile(`(?i)--[^:]*:\s*url\s*\(\s*["']?([^"')]+)["']?\s*\)`),

                // 媒体查询中的 URL
                regexp.MustCompile(`(?i)@media[^{]*\{[^}]*url\s*\(\s*["']?([^"')]+)["']?\s*\)`),
        }

        // JavaScript 框架特定模式
        rewriter.jsFrameworkPatterns = []*regexp.Regexp{
                // React/Vue 等框架的资源引用
                regexp.MustCompile(`(?i)require\s*\(\s*["']([^"']+)["']\s*\)`),
                regexp.MustCompile(`(?i)import\s+[^"']*["']([^"']+)["']`),

                // Angular 的模板 URL
                regexp.MustCompile(`(?i)templateUrl\s*:\s*["']([^"']+)["']`),
                regexp.MustCompile(`(?i)styleUrls\s*:\s*\[\s*["']([^"']+)["']`),

                // Vue 的组件引用
                regexp.MustCompile(`(?i)component\s*:\s*\(\s*\)\s*=&gt;\s*import\s*\(\s*["']([^"']+)["']`),
        }

        return rewriter
}</span>

// RewriteAdvancedJS 高级 JavaScript 改写
func (ar *AdvancedRewriter) RewriteAdvancedJS(content []byte, targetDomain string, domainPorts map[string]int, baseRewriter *ContentRewriter) []byte <span class="cov8" title="1">{
        result := content

        // 应用高级 JavaScript 模式
        for _, regex := range ar.jsAdvancedRegexes </span><span class="cov8" title="1">{
                result = regex.ReplaceAllFunc(result, func(match []byte) []byte </span><span class="cov8" title="1">{
                        return ar.rewriteAdvancedJSMatch(match, regex, targetDomain, domainPorts, baseRewriter)
                }</span>)
        }

        // 应用框架特定模式
        <span class="cov8" title="1">for _, regex := range ar.jsFrameworkPatterns </span><span class="cov8" title="1">{
                result = regex.ReplaceAllFunc(result, func(match []byte) []byte </span><span class="cov0" title="0">{
                        return ar.rewriteFrameworkMatch(match, regex, targetDomain, domainPorts, baseRewriter)
                }</span>)
        }

        <span class="cov8" title="1">return result</span>
}

// RewriteAdvancedCSS 高级 CSS 改写
func (ar *AdvancedRewriter) RewriteAdvancedCSS(content []byte, targetDomain string, domainPorts map[string]int, baseRewriter *ContentRewriter) []byte <span class="cov8" title="1">{
        result := content

        // 应用高级 CSS 模式
        for _, regex := range ar.cssAdvancedRegexes </span><span class="cov8" title="1">{
                result = regex.ReplaceAllFunc(result, func(match []byte) []byte </span><span class="cov8" title="1">{
                        return ar.rewriteAdvancedCSSMatch(match, regex, targetDomain, domainPorts, baseRewriter)
                }</span>)
        }

        <span class="cov8" title="1">return result</span>
}

// rewriteAdvancedJSMatch 改写高级 JavaScript 匹配
func (ar *AdvancedRewriter) rewriteAdvancedJSMatch(match []byte, regex *regexp.Regexp, targetDomain string, domainPorts map[string]int, baseRewriter *ContentRewriter) []byte <span class="cov8" title="1">{
        parts := regex.FindSubmatch(match)
        if len(parts) &lt; 2 </span><span class="cov0" title="0">{
                return match
        }</span>

        <span class="cov8" title="1">matchStr := string(match)
        urlStr := string(parts[1])

        // 使用基础改写器改写 URL
        newURL := baseRewriter.rewriteURL(urlStr, targetDomain, domainPorts)

        // 替换原始 URL
        return []byte(strings.Replace(matchStr, urlStr, newURL, 1))</span>
}

// rewriteAdvancedCSSMatch 改写高级 CSS 匹配
func (ar *AdvancedRewriter) rewriteAdvancedCSSMatch(match []byte, regex *regexp.Regexp, targetDomain string, domainPorts map[string]int, baseRewriter *ContentRewriter) []byte <span class="cov8" title="1">{
        parts := regex.FindSubmatch(match)
        if len(parts) &lt; 2 </span><span class="cov0" title="0">{
                return match
        }</span>

        <span class="cov8" title="1">matchStr := string(match)
        urlStr := string(parts[1])

        // 使用基础改写器改写 URL
        newURL := baseRewriter.rewriteURL(urlStr, targetDomain, domainPorts)

        // 替换原始 URL
        return []byte(strings.Replace(matchStr, urlStr, newURL, 1))</span>
}

// rewriteFrameworkMatch 改写框架特定匹配
func (ar *AdvancedRewriter) rewriteFrameworkMatch(match []byte, regex *regexp.Regexp, targetDomain string, domainPorts map[string]int, baseRewriter *ContentRewriter) []byte <span class="cov0" title="0">{
        parts := regex.FindSubmatch(match)
        if len(parts) &lt; 2 </span><span class="cov0" title="0">{
                return match
        }</span>

        <span class="cov0" title="0">matchStr := string(match)
        urlStr := string(parts[1])

        // 对于模块导入，只改写 HTTP/HTTPS URL
        if strings.HasPrefix(urlStr, "http://") || strings.HasPrefix(urlStr, "https://") </span><span class="cov0" title="0">{
                newURL := baseRewriter.rewriteURL(urlStr, targetDomain, domainPorts)
                return []byte(strings.Replace(matchStr, urlStr, newURL, 1))
        }</span>

        // 对于相对路径的模块导入，保持不变
        <span class="cov0" title="0">return match</span>
}

// DetectContentType 检测内容类型的更精确方法
func (ar *AdvancedRewriter) DetectContentType(content []byte, declaredType string) string <span class="cov0" title="0">{
        contentStr := string(content)

        // 检测 JavaScript
        if strings.Contains(declaredType, "javascript") ||
                strings.Contains(contentStr, "function") ||
                strings.Contains(contentStr, "var ") ||
                strings.Contains(contentStr, "let ") ||
                strings.Contains(contentStr, "const ") ||
                strings.Contains(contentStr, "=&gt;") </span><span class="cov0" title="0">{
                return "javascript"
        }</span>

        // 检测 CSS
        <span class="cov0" title="0">if strings.Contains(declaredType, "css") ||
                strings.Contains(contentStr, "{") &amp;&amp; strings.Contains(contentStr, "}") &amp;&amp;
                        (strings.Contains(contentStr, "color:") || strings.Contains(contentStr, "background:") || strings.Contains(contentStr, "font-")) </span><span class="cov0" title="0">{
                return "css"
        }</span>

        // 检测 HTML
        <span class="cov0" title="0">if strings.Contains(declaredType, "html") ||
                strings.Contains(contentStr, "&lt;html") ||
                strings.Contains(contentStr, "&lt;!DOCTYPE") ||
                strings.Contains(contentStr, "&lt;head&gt;") ||
                strings.Contains(contentStr, "&lt;body&gt;") </span><span class="cov0" title="0">{
                return "html"
        }</span>

        // 检测 JSON
        <span class="cov0" title="0">if strings.Contains(declaredType, "json") ||
                (strings.HasPrefix(strings.TrimSpace(contentStr), "{") &amp;&amp; strings.HasSuffix(strings.TrimSpace(contentStr), "}")) ||
                (strings.HasPrefix(strings.TrimSpace(contentStr), "[") &amp;&amp; strings.HasSuffix(strings.TrimSpace(contentStr), "]")) </span><span class="cov0" title="0">{
                return "json"
        }</span>

        <span class="cov0" title="0">return "unknown"</span>
}

// ShouldRewriteURL 判断是否应该改写特定的 URL
func (ar *AdvancedRewriter) ShouldRewriteURL(urlStr string) bool <span class="cov0" title="0">{
        // 跳过特殊协议
        skipProtocols := []string{"data:", "javascript:", "mailto:", "tel:", "ftp:", "file:"}
        for _, protocol := range skipProtocols </span><span class="cov0" title="0">{
                if strings.HasPrefix(strings.ToLower(urlStr), protocol) </span><span class="cov0" title="0">{
                        return false
                }</span>
        }

        // 跳过锚点链接
        <span class="cov0" title="0">if strings.HasPrefix(urlStr, "#") </span><span class="cov0" title="0">{
                return false
        }</span>

        // 跳过空 URL
        <span class="cov0" title="0">if strings.TrimSpace(urlStr) == "" </span><span class="cov0" title="0">{
                return false
        }</span>

        <span class="cov0" title="0">return true</span>
}
</pre>
		
		<pre class="file" id="file2" style="display: none">package rewriter

import (
        "fmt"
        "net/url"
        "regexp"
        "strings"

        "mirrorgo/pkg/config"
)

// ContentRewriter 内容改写器
type ContentRewriter struct {
        config *config.Config

        // HTML 相关的正则表达式
        htmlAttrRegexes map[string]*regexp.Regexp

        // CSS 相关的正则表达式
        cssURLRegex *regexp.Regexp

        // JavaScript 相关的正则表达式
        jsURLRegexes []*regexp.Regexp

        // 高级改写器
        advancedRewriter *AdvancedRewriter
}

// NewContentRewriter 创建新的内容改写器
func NewContentRewriter(cfg *config.Config) *ContentRewriter <span class="cov8" title="1">{
        rewriter := &amp;ContentRewriter{
                config:           cfg,
                htmlAttrRegexes:  make(map[string]*regexp.Regexp),
                advancedRewriter: NewAdvancedRewriter(),
        }

        // 编译 HTML 属性正则表达式
        for _, attr := range cfg.RewriteConfig.HTMLAttributes </span><span class="cov8" title="1">{
                pattern := fmt.Sprintf(`(?i)(\s%s\s*=\s*["'])([^"']+)(["'])`, attr)
                rewriter.htmlAttrRegexes[attr] = regexp.MustCompile(pattern)
        }</span>

        // 编译 CSS URL 正则表达式
        <span class="cov8" title="1">rewriter.cssURLRegex = regexp.MustCompile(`(?i)url\s*\(\s*["']?([^"')]+)["']?\s*\)`)

        // 编译 JavaScript URL 正则表达式
        rewriter.jsURLRegexes = []*regexp.Regexp{
                regexp.MustCompile(`(?i)["']https?://[^"']+["']`),
                regexp.MustCompile(`(?i)location\.href\s*=\s*["']([^"']+)["']`),
                regexp.MustCompile(`(?i)window\.open\s*\(\s*["']([^"']+)["']`),
                regexp.MustCompile(`(?i)fetch\s*\(\s*["']([^"']+)["']`),
                regexp.MustCompile(`(?i)XMLHttpRequest.*\.open\s*\(\s*["'][^"']*["']\s*,\s*["']([^"']+)["']`),
        }

        return rewriter</span>
}

// RewriteContent 改写内容
func (r *ContentRewriter) RewriteContent(content []byte, contentType, targetDomain string, domainPorts map[string]int) ([]byte, error) <span class="cov8" title="1">{
        contentType = strings.ToLower(contentType)

        if r.config.RewriteConfig.EnableHTML &amp;&amp; strings.Contains(contentType, "text/html") </span><span class="cov8" title="1">{
                return r.rewriteHTML(content, targetDomain, domainPorts)
        }</span>

        <span class="cov8" title="1">if r.config.RewriteConfig.EnableCSS &amp;&amp; strings.Contains(contentType, "text/css") </span><span class="cov8" title="1">{
                return r.rewriteCSS(content, targetDomain, domainPorts)
        }</span>

        <span class="cov8" title="1">if r.config.RewriteConfig.EnableJS &amp;&amp; (strings.Contains(contentType, "javascript") || strings.Contains(contentType, "application/json")) </span><span class="cov8" title="1">{
                return r.rewriteJS(content, targetDomain, domainPorts)
        }</span>

        <span class="cov8" title="1">return content, nil</span>
}

// rewriteHTML 改写 HTML 内容
func (r *ContentRewriter) rewriteHTML(content []byte, targetDomain string, domainPorts map[string]int) ([]byte, error) <span class="cov8" title="1">{
        result := content

        // 改写各种 HTML 属性
        for attr, regex := range r.htmlAttrRegexes </span><span class="cov8" title="1">{
                result = regex.ReplaceAllFunc(result, func(match []byte) []byte </span><span class="cov8" title="1">{
                        return r.rewriteHTMLAttribute(match, attr, targetDomain, domainPorts)
                }</span>)
        }

        // 改写内联样式中的 URL
        <span class="cov8" title="1">if r.config.RewriteConfig.EnableCSS </span><span class="cov8" title="1">{
                styleRegex := regexp.MustCompile(`(?i)(\sstyle\s*=\s*["'])([^"']+)(["'])`)
                result = styleRegex.ReplaceAllFunc(result, func(match []byte) []byte </span><span class="cov0" title="0">{
                        return r.rewriteInlineStyle(match, targetDomain, domainPorts)
                }</span>)
        }

        // 改写内联脚本中的 URL
        <span class="cov8" title="1">if r.config.RewriteConfig.EnableJS </span><span class="cov8" title="1">{
                scriptRegex := regexp.MustCompile(`(?i)(&lt;script[^&gt;]*&gt;)(.*?)(&lt;/script&gt;)`)
                result = scriptRegex.ReplaceAllFunc(result, func(match []byte) []byte </span><span class="cov0" title="0">{
                        return r.rewriteInlineScript(match, targetDomain, domainPorts)
                }</span>)
        }

        <span class="cov8" title="1">return result, nil</span>
}

// rewriteHTMLAttribute 改写 HTML 属性
func (r *ContentRewriter) rewriteHTMLAttribute(match []byte, attr, targetDomain string, domainPorts map[string]int) []byte <span class="cov8" title="1">{
        parts := r.htmlAttrRegexes[attr].FindSubmatch(match)
        if len(parts) != 4 </span><span class="cov0" title="0">{
                return match
        }</span>

        <span class="cov8" title="1">prefix := parts[1]         // 属性名和等号
        urlStr := string(parts[2]) // URL 值
        suffix := parts[3]         // 引号

        // 改写 URL
        newURL := r.rewriteURL(urlStr, targetDomain, domainPorts)

        return append(append(prefix, []byte(newURL)...), suffix...)</span>
}

// rewriteInlineStyle 改写内联样式
func (r *ContentRewriter) rewriteInlineStyle(match []byte, targetDomain string, domainPorts map[string]int) []byte <span class="cov0" title="0">{
        styleRegex := regexp.MustCompile(`(?i)(\sstyle\s*=\s*["'])([^"']+)(["'])`)
        parts := styleRegex.FindSubmatch(match)
        if len(parts) != 4 </span><span class="cov0" title="0">{
                return match
        }</span>

        <span class="cov0" title="0">prefix := parts[1]
        styleContent := parts[2]
        suffix := parts[3]

        // 改写样式中的 URL
        newStyleContent := r.cssURLRegex.ReplaceAllFunc(styleContent, func(urlMatch []byte) []byte </span><span class="cov0" title="0">{
                return r.rewriteCSSURL(urlMatch, targetDomain, domainPorts)
        }</span>)

        <span class="cov0" title="0">return append(append(prefix, newStyleContent...), suffix...)</span>
}

// rewriteInlineScript 改写内联脚本
func (r *ContentRewriter) rewriteInlineScript(match []byte, targetDomain string, domainPorts map[string]int) []byte <span class="cov0" title="0">{
        scriptRegex := regexp.MustCompile(`(?i)(&lt;script[^&gt;]*&gt;)(.*?)(&lt;/script&gt;)`)
        parts := scriptRegex.FindSubmatch(match)
        if len(parts) != 4 </span><span class="cov0" title="0">{
                return match
        }</span>

        <span class="cov0" title="0">prefix := parts[1]
        scriptContent := parts[2]
        suffix := parts[3]

        // 改写脚本中的 URL
        newScriptContent := scriptContent
        for _, regex := range r.jsURLRegexes </span><span class="cov0" title="0">{
                newScriptContent = regex.ReplaceAllFunc(newScriptContent, func(urlMatch []byte) []byte </span><span class="cov0" title="0">{
                        return r.rewriteJSURL(urlMatch, targetDomain, domainPorts)
                }</span>)
        }

        <span class="cov0" title="0">return append(append(prefix, newScriptContent...), suffix...)</span>
}

// rewriteCSS 改写 CSS 内容
func (r *ContentRewriter) rewriteCSS(content []byte, targetDomain string, domainPorts map[string]int) ([]byte, error) <span class="cov8" title="1">{
        // 基础改写
        result := r.cssURLRegex.ReplaceAllFunc(content, func(match []byte) []byte </span><span class="cov8" title="1">{
                return r.rewriteCSSURL(match, targetDomain, domainPorts)
        }</span>)

        // 高级改写
        <span class="cov8" title="1">result = r.advancedRewriter.RewriteAdvancedCSS(result, targetDomain, domainPorts, r)

        return result, nil</span>
}

// rewriteCSSURL 改写 CSS 中的 URL
func (r *ContentRewriter) rewriteCSSURL(match []byte, targetDomain string, domainPorts map[string]int) []byte <span class="cov8" title="1">{
        parts := r.cssURLRegex.FindSubmatch(match)
        if len(parts) != 2 </span><span class="cov0" title="0">{
                return match
        }</span>

        <span class="cov8" title="1">urlStr := string(parts[1])
        newURL := r.rewriteURL(urlStr, targetDomain, domainPorts)

        return []byte(fmt.Sprintf("url(%s)", newURL))</span>
}

// rewriteJS 改写 JavaScript 内容
func (r *ContentRewriter) rewriteJS(content []byte, targetDomain string, domainPorts map[string]int) ([]byte, error) <span class="cov8" title="1">{
        result := content

        // 基础改写
        for _, regex := range r.jsURLRegexes </span><span class="cov8" title="1">{
                result = regex.ReplaceAllFunc(result, func(match []byte) []byte </span><span class="cov8" title="1">{
                        return r.rewriteJSURL(match, targetDomain, domainPorts)
                }</span>)
        }

        // 高级改写
        <span class="cov8" title="1">result = r.advancedRewriter.RewriteAdvancedJS(result, targetDomain, domainPorts, r)

        return result, nil</span>
}

// rewriteJSURL 改写 JavaScript 中的 URL
func (r *ContentRewriter) rewriteJSURL(match []byte, targetDomain string, domainPorts map[string]int) []byte <span class="cov8" title="1">{
        matchStr := string(match)

        // 提取 URL 部分
        var urlStr string
        if strings.Contains(matchStr, `"`) </span><span class="cov8" title="1">{
                parts := strings.Split(matchStr, `"`)
                if len(parts) &gt;= 3 </span><span class="cov8" title="1">{
                        urlStr = parts[1]
                }</span>
        } else<span class="cov0" title="0"> if strings.Contains(matchStr, `'`) </span><span class="cov0" title="0">{
                parts := strings.Split(matchStr, `'`)
                if len(parts) &gt;= 3 </span><span class="cov0" title="0">{
                        urlStr = parts[1]
                }</span>
        }

        <span class="cov8" title="1">if urlStr == "" </span><span class="cov0" title="0">{
                return match
        }</span>

        <span class="cov8" title="1">newURL := r.rewriteURL(urlStr, targetDomain, domainPorts)
        return []byte(strings.Replace(matchStr, urlStr, newURL, 1))</span>
}

// rewriteURL 改写 URL
func (r *ContentRewriter) rewriteURL(urlStr, currentDomain string, domainPorts map[string]int) string <span class="cov8" title="1">{
        // 跳过空 URL 和特殊协议
        if urlStr == "" || strings.HasPrefix(urlStr, "data:") || strings.HasPrefix(urlStr, "javascript:") || strings.HasPrefix(urlStr, "mailto:") </span><span class="cov8" title="1">{
                return urlStr
        }</span>

        // 解析 URL
        <span class="cov8" title="1">parsedURL, err := url.Parse(urlStr)
        if err != nil </span><span class="cov0" title="0">{
                return urlStr
        }</span>

        // 处理相对 URL
        <span class="cov8" title="1">if parsedURL.Host == "" </span><span class="cov8" title="1">{
                if strings.HasPrefix(urlStr, "//") </span><span class="cov0" title="0">{
                        // 协议相对 URL
                        parsedURL.Scheme = "https"
                        parsedURL.Host = strings.TrimPrefix(urlStr, "//")
                        if idx := strings.Index(parsedURL.Host, "/"); idx != -1 </span><span class="cov0" title="0">{
                                parsedURL.Path = parsedURL.Host[idx:]
                                parsedURL.Host = parsedURL.Host[:idx]
                        }</span>
                } else<span class="cov8" title="1"> if strings.HasPrefix(urlStr, "/") </span><span class="cov8" title="1">{
                        // 绝对路径
                        if port, exists := domainPorts[currentDomain]; exists </span><span class="cov8" title="1">{
                                return fmt.Sprintf("http://localhost:%d%s", port, urlStr)
                        }</span>
                        <span class="cov8" title="1">return urlStr</span>
                } else<span class="cov0" title="0"> {
                        // 相对路径，保持不变
                        return urlStr
                }</span>
        }

        // 检查是否需要代理这个域名
        <span class="cov8" title="1">if port, exists := domainPorts[parsedURL.Host]; exists </span><span class="cov8" title="1">{
                // 构建本地代理 URL
                localURL := fmt.Sprintf("http://localhost:%d%s", port, parsedURL.Path)
                if parsedURL.RawQuery != "" </span><span class="cov0" title="0">{
                        localURL += "?" + parsedURL.RawQuery
                }</span>
                <span class="cov8" title="1">if parsedURL.Fragment != "" </span><span class="cov0" title="0">{
                        localURL += "#" + parsedURL.Fragment
                }</span>
                <span class="cov8" title="1">return localURL</span>
        }

        // 对于未配置的域名，使用代理路径格式
        <span class="cov8" title="1">scheme := parsedURL.Scheme
        if scheme == "" </span><span class="cov0" title="0">{
                scheme = "https"
        }</span>

        <span class="cov8" title="1">proxyURL := fmt.Sprintf("/proxy/%s/%s%s", scheme, parsedURL.Host, parsedURL.Path)
        if parsedURL.RawQuery != "" </span><span class="cov0" title="0">{
                proxyURL += "?" + parsedURL.RawQuery
        }</span>
        <span class="cov8" title="1">if parsedURL.Fragment != "" </span><span class="cov0" title="0">{
                proxyURL += "#" + parsedURL.Fragment
        }</span>

        <span class="cov8" title="1">return proxyURL</span>
}
</pre>
		
		</div>
	</body>
	<script>
	(function() {
		var files = document.getElementById('files');
		var visible;
		files.addEventListener('change', onChange, false);
		function select(part) {
			if (visible)
				visible.style.display = 'none';
			visible = document.getElementById(part);
			if (!visible)
				return;
			files.value = part;
			visible.style.display = 'block';
			location.hash = part;
		}
		function onChange() {
			select(files.value);
			window.scrollTo(0, 0);
		}
		if (location.hash != "") {
			select(location.hash.substr(1));
		}
		if (!visible) {
			select("file0");
		}
	})();
	</script>
</html>

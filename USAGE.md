# MirrorGo 使用指南

## 📖 概述

MirrorGo 是一个基于多端口的反向代理系统，专为绕过网络限制而设计。它为每个目标域名分配独立的本地端口，并自动改写网页内容中的链接，使浏览器能够无缝访问被屏蔽的网站。

## 🚀 快速开始

### 1. 编译项目

```bash
# 克隆项目
git clone <your-repo-url>
cd mirrorgo

# 编译
go build -o mirrorgo cmd/mirrorgo/main.go
```

### 2. 配置服务

编辑 `config.yaml` 文件：

```yaml
# 基本配置：域名到端口的映射
domain_ports:
  github.com: 8080
  stackoverflow.com: 8081
  developer.mozilla.org: 8082
```

### 3. 启动服务

```bash
./mirrorgo -config config.yaml
```

### 4. 访问网站

- GitHub: http://localhost:8080
- Stack Overflow: http://localhost:8081
- MDN: http://localhost:8082

## ⚙️ 高级配置

### 完整配置示例

```yaml
# 域名端口映射
domain_ports:
  github.com: 8080
  stackoverflow.com: 8081
  npmjs.com: 8082

# 上游代理配置（可选）
proxy:
  enable: true
  url: "http://proxy.company.com:8080"
  username: "your_username"  # 可选
  password: "your_password"  # 可选

# 内容改写配置
rewrite:
  enable_html: true     # 启用 HTML 改写
  enable_js: true       # 启用 JavaScript 改写
  enable_css: true      # 启用 CSS 改写
  
  # 自定义需要改写的 HTML 属性
  html_attributes:
    - "src"
    - "href"
    - "action"
    - "data-src"
    - "data-href"
    - "poster"
    - "background"

# 服务器配置
server:
  read_timeout: 30      # 读取超时（秒）
  write_timeout: 30     # 写入超时（秒）
  idle_timeout: 60      # 空闲超时（秒）
```

## 🔧 工作原理

### 1. 多端口架构

```
用户浏览器 → localhost:8080 → MirrorGo → github.com
用户浏览器 → localhost:8081 → MirrorGo → stackoverflow.com
```

### 2. 链接改写机制

**原始 HTML:**
```html
<a href="https://github.com/user/repo">Repository</a>
<img src="https://avatars.githubusercontent.com/u/123">
<script src="https://github.githubassets.com/assets/app.js"></script>
```

**改写后:**
```html
<a href="http://localhost:8080/user/repo">Repository</a>
<img src="/proxy/https/avatars.githubusercontent.com/u/123">
<script src="/proxy/https/github.githubassets.com/assets/app.js"></script>
```

### 3. 支持的内容类型

- **HTML**: 改写 `src`、`href`、`action` 等属性
- **CSS**: 改写 `url()` 函数中的链接
- **JavaScript**: 改写 `fetch()`、`XMLHttpRequest` 等 API 调用

## 🛠️ 使用场景

### 场景 1: 开发环境代理

```yaml
domain_ports:
  api.github.com: 8080
  raw.githubusercontent.com: 8081
```

### 场景 2: 企业网络环境

```yaml
domain_ports:
  stackoverflow.com: 8080
  github.com: 8081
  npmjs.com: 8082

proxy:
  enable: true
  url: "http://corporate-proxy:8080"
  username: "employee_id"
  password: "password"
```

### 场景 3: 学习研究环境

```yaml
domain_ports:
  developer.mozilla.org: 8080
  w3schools.com: 8081
  codepen.io: 8082
```

## 🔍 故障排除

### 常见问题

1. **端口被占用**
   ```
   Error: listen tcp :8080: bind: address already in use
   ```
   解决方案：更改配置文件中的端口号

2. **目标网站拒绝连接**
   ```
   HTTP/1.1 403 Forbidden
   ```
   解决方案：某些网站有反爬虫保护，这是正常现象

3. **代理连接失败**
   ```
   Error: proxy connection failed
   ```
   解决方案：检查上游代理配置是否正确

### 调试技巧

1. **查看请求头**
   ```bash
   curl -I http://localhost:8080
   ```

2. **测试特定路径**
   ```bash
   curl http://localhost:8080/user/repo
   ```

3. **检查改写效果**
   ```bash
   curl -s http://localhost:8080 | grep -o 'localhost:8080'
   ```

## 📊 性能优化

### 1. 调整超时设置

```yaml
server:
  read_timeout: 60      # 增加读取超时
  write_timeout: 60     # 增加写入超时
  idle_timeout: 120     # 增加空闲超时
```

### 2. 禁用不必要的改写

```yaml
rewrite:
  enable_html: true
  enable_js: false      # 如果不需要 JS 改写
  enable_css: false     # 如果不需要 CSS 改写
```

## 🔒 安全注意事项

1. **仅在受信任的网络环境中使用**
2. **不要在配置文件中明文存储敏感密码**
3. **定期更新代理服务器软件**
4. **监控代理服务器的访问日志**

## 🧪 测试

运行单元测试：

```bash
# 运行所有测试
go test ./...

# 运行特定模块测试
go test ./pkg/config -v
go test ./pkg/rewriter -v

# 运行性能测试
go test -bench=. ./...
```

## 📝 开发

### 项目结构

```
mirrorgo/
├── cmd/mirrorgo/          # 主程序入口
├── pkg/
│   ├── config/           # 配置管理
│   ├── proxy/            # 代理服务器
│   ├── rewriter/         # 内容改写器
│   └── utils/            # 工具函数
├── examples/             # 示例配置
├── tests/               # 测试文件
└── docs/                # 文档
```

### 添加新功能

1. 在相应的 `pkg/` 子目录中添加代码
2. 编写对应的测试用例
3. 更新配置结构（如需要）
4. 更新文档

## 📄 许可证

本项目采用 MIT 许可证。详见 LICENSE 文件。

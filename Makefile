# MirrorGo Makefile

# 变量定义
BINARY_NAME=mirrorgo
MAIN_PATH=cmd/mirrorgo/main.go
BUILD_DIR=build
CONFIG_FILE=config.yaml

# Go 相关变量
GOCMD=go
GOBUILD=$(GOCMD) build
GOCLEAN=$(GOCMD) clean
GOTEST=$(GOCMD) test
GOGET=$(GOCMD) get
GOMOD=$(GOCMD) mod

# 构建信息
VERSION?=1.0.0
BUILD_TIME=$(shell date +%Y-%m-%d_%H:%M:%S)
GIT_COMMIT=$(shell git rev-parse --short HEAD 2>/dev/null || echo "unknown")

# 构建标志
LDFLAGS=-ldflags "-X main.Version=$(VERSION) -X main.BuildTime=$(BUILD_TIME) -X main.GitCommit=$(GIT_COMMIT)"

.PHONY: all build clean test test-verbose test-coverage run help deps fmt vet lint

# 默认目标
all: clean deps test build

# 构建二进制文件
build:
	@echo "构建 $(BINARY_NAME)..."
	@mkdir -p $(BUILD_DIR)
	$(GOBUILD) $(LDFLAGS) -o $(BUILD_DIR)/$(BINARY_NAME) $(MAIN_PATH)
	@echo "构建完成: $(BUILD_DIR)/$(BINARY_NAME)"

# 构建多平台版本
build-all: clean deps test
	@echo "构建多平台版本..."
	@mkdir -p $(BUILD_DIR)
	
	# Linux AMD64
	GOOS=linux GOARCH=amd64 $(GOBUILD) $(LDFLAGS) -o $(BUILD_DIR)/$(BINARY_NAME)-linux-amd64 $(MAIN_PATH)
	
	# Linux ARM64
	GOOS=linux GOARCH=arm64 $(GOBUILD) $(LDFLAGS) -o $(BUILD_DIR)/$(BINARY_NAME)-linux-arm64 $(MAIN_PATH)
	
	# macOS AMD64
	GOOS=darwin GOARCH=amd64 $(GOBUILD) $(LDFLAGS) -o $(BUILD_DIR)/$(BINARY_NAME)-darwin-amd64 $(MAIN_PATH)
	
	# macOS ARM64 (Apple Silicon)
	GOOS=darwin GOARCH=arm64 $(GOBUILD) $(LDFLAGS) -o $(BUILD_DIR)/$(BINARY_NAME)-darwin-arm64 $(MAIN_PATH)
	
	# Windows AMD64
	GOOS=windows GOARCH=amd64 $(GOBUILD) $(LDFLAGS) -o $(BUILD_DIR)/$(BINARY_NAME)-windows-amd64.exe $(MAIN_PATH)
	
	@echo "多平台构建完成"

# 清理构建文件
clean:
	@echo "清理构建文件..."
	$(GOCLEAN)
	@rm -rf $(BUILD_DIR)
	@echo "清理完成"

# 安装依赖
deps:
	@echo "安装依赖..."
	$(GOMOD) download
	$(GOMOD) tidy
	@echo "依赖安装完成"

# 运行测试
test:
	@echo "运行测试..."
	$(GOTEST) ./...

# 详细测试输出
test-verbose:
	@echo "运行详细测试..."
	$(GOTEST) -v ./...

# 测试覆盖率
test-coverage:
	@echo "运行测试覆盖率分析..."
	$(GOTEST) -coverprofile=coverage.out ./...
	$(GOCMD) tool cover -html=coverage.out -o coverage.html
	@echo "覆盖率报告生成: coverage.html"

# 运行应用程序
run: build
	@echo "启动 $(BINARY_NAME)..."
	@if [ ! -f $(CONFIG_FILE) ]; then \
		echo "配置文件 $(CONFIG_FILE) 不存在，请先创建配置文件"; \
		exit 1; \
	fi
	./$(BUILD_DIR)/$(BINARY_NAME) -config $(CONFIG_FILE)

# 开发模式运行（直接运行源码）
dev:
	@echo "开发模式启动..."
	@if [ ! -f $(CONFIG_FILE) ]; then \
		echo "配置文件 $(CONFIG_FILE) 不存在，请先创建配置文件"; \
		exit 1; \
	fi
	$(GOCMD) run $(MAIN_PATH) -config $(CONFIG_FILE)

# 格式化代码
fmt:
	@echo "格式化代码..."
	$(GOCMD) fmt ./...

# 代码检查
vet:
	@echo "运行 go vet..."
	$(GOCMD) vet ./...

# 代码质量检查（需要安装 golangci-lint）
lint:
	@echo "运行代码质量检查..."
	@if command -v golangci-lint >/dev/null 2>&1; then \
		golangci-lint run; \
	else \
		echo "golangci-lint 未安装，跳过代码质量检查"; \
		echo "安装命令: go install github.com/golangci/golangci-lint/cmd/golangci-lint@latest"; \
	fi

# 安装到系统
install: build
	@echo "安装 $(BINARY_NAME) 到系统..."
	@sudo cp $(BUILD_DIR)/$(BINARY_NAME) /usr/local/bin/
	@echo "安装完成: /usr/local/bin/$(BINARY_NAME)"

# 卸载
uninstall:
	@echo "卸载 $(BINARY_NAME)..."
	@sudo rm -f /usr/local/bin/$(BINARY_NAME)
	@echo "卸载完成"

# 创建示例配置
config:
	@if [ ! -f $(CONFIG_FILE) ]; then \
		echo "创建示例配置文件..."; \
		cp examples/config.yaml $(CONFIG_FILE); \
		echo "配置文件已创建: $(CONFIG_FILE)"; \
		echo "请根据需要修改配置文件"; \
	else \
		echo "配置文件已存在: $(CONFIG_FILE)"; \
	fi

# 检查代码质量
check: fmt vet test

# 发布准备
release: clean deps check build-all
	@echo "发布准备完成"
	@echo "构建文件位于: $(BUILD_DIR)/"
	@ls -la $(BUILD_DIR)/

# 显示帮助信息
help:
	@echo "MirrorGo 构建工具"
	@echo ""
	@echo "可用命令:"
	@echo "  build        构建二进制文件"
	@echo "  build-all    构建多平台版本"
	@echo "  clean        清理构建文件"
	@echo "  deps         安装依赖"
	@echo "  test         运行测试"
	@echo "  test-verbose 运行详细测试"
	@echo "  test-coverage 生成测试覆盖率报告"
	@echo "  run          构建并运行应用程序"
	@echo "  dev          开发模式运行"
	@echo "  fmt          格式化代码"
	@echo "  vet          代码检查"
	@echo "  lint         代码质量检查"
	@echo "  install      安装到系统"
	@echo "  uninstall    从系统卸载"
	@echo "  config       创建示例配置文件"
	@echo "  check        检查代码质量（fmt + vet + test）"
	@echo "  release      发布准备（构建所有平台版本）"
	@echo "  help         显示此帮助信息"
	@echo ""
	@echo "示例:"
	@echo "  make build          # 构建应用程序"
	@echo "  make config         # 创建配置文件"
	@echo "  make run            # 运行应用程序"
	@echo "  make test-coverage  # 生成测试覆盖率报告"
